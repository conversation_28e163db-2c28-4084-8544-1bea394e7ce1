"use client";
import React, { useState } from "react";
import { AiOutlineEdit, AiOutlineDelete } from "react-icons/ai";
import { MdOutlineCheckCircle, MdOutlineVisibilityOff } from "react-icons/md";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import CustomSearch from "@/components/ui/custom/CustomSearchBar";
import PaginationComponent from "../../_components/PaginationComponent";
import Link from "next/link";
import CustomTabsTrigger from "@/components/ui/custom/CustomTabsTrigger";
import { Button } from "@/components/ui/button";
import VerificationGuard from "@/lib/components/VerificationGuard";


const sampleBales = [
  {
    id: 1,
    name: "Poly silk dress",
    image: "/images/bales/bales1.jpg",
    stock: 25,
    price: "GH₵45",
    category: "Jackets",
    status: "Accepted",
    live: true,
  },
  {
    id: 2,
    name: "Polo Dress",
    image: "/images/bales/bales2.jpg",
    stock: 0,
    price: "GH₵60",
    category: "Dresses",
    status: "Rejected",
    live: false,
  },
  {
    id: 3,
    name: "Polo Blouse",
    image: "/images/bales/bales3.jpg",
    stock: 5,
    price: "GH₵120",
    category: "Shoes",
    status: "Pending",
    live: false,
  },
  {
    id: 4,
    name: "Polo Skirt",
    image: "/images/bales/bales4.jpg",
    stock: 50,
    price: "GH₵25",
    category: "Accessories",
    status: "Accepted",
    live: true,
  },
  {
    id: 5,
    name: "Jeans Dress",
    image: "/images/bales/bales2.jpg",
    stock: 5,
    price: "GH₵120",
    category: "Shoes",
    status: "Pending",
    live: false,
  },
  {
    id: 6,
    name: "Jeans Trousers",
    image: "/images/bales/bales3.jpg",
    stock: 50,
    price: "GH₵25",
    category: "Accessories",
    status: "Accepted",
    live: true,
  },
  {
    id: 7,
    name: "Jeans shorts",
    image: "/images/bales/bales1.jpg",
    stock: 5,
    price: "GH₵120",
    category: "Shoes",
    status: "Pending",
    live: false,
  },
  {
    id: 8,
    name: "Cotton Dress",
    image: "/images/bales/bales4.jpg",
    stock: 50,
    price: "GH₵25",
    category: "Accessories",
    status: "Accepted",
    live: true,
  },
  {
    id: 9,
    name: "Cotton Blouse",
    image: "/images/bales/bales2.jpg",
    stock: 5,
    price: "GH₵120",
    category: "Shoes",
    status: "Pending",
    live: false,
  },
  {
    id: 10,
    name: "Cotton Skirt",
    image: "/images/bales/bales3.jpg",
    stock: 50,
    price: "GH₵25",
    category: "Accessories",
    status: "Accepted",
    live: true,
  },
];

const FashionBalesPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const totalPages = 10;

  const filteredProducts = sampleBales.filter((bale) => {

    if (activeTab === "low-stock") return bale.stock > 0 && bale.stock <= 10 ;
    if (activeTab === "out-of-stock") return bale.stock === 0 ;
    if (activeTab === "accepted") return bale.status === "Accepted" ;
    if (activeTab === "rejected") return bale.status === "Rejected" ;
    if (activeTab === "live") return bale.live ;
    if (activeTab === "not-live") return !bale.live ;
    return bale;
  });

  return (
    <VerificationGuard feature="bales">
      <section className="w-full max-w-md mx-auto py-4">
     {/* Header */}
     <div className="flex justify-between items-center mb-4">
        <h1 className="text-sm font-bold uppercase text-gray-600">Manage Bales</h1>
        <Link
          href="/creators/bales/add/bale-info"
          className="bg-primary text-primary-foreground text-sm px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors duration-200"
        >
          Add Bale
        </Link>
      </div>
    <section className="w-full mb-4">
        <CustomSearch
            placeholder="Search for bales..."
        />
    </section>


      {/* Filter Tabs */}
      <Tabs defaultValue="all" onValueChange={(value) => setActiveTab(value)} className="mb-6">
        <TabsList className="bg-gray-100  w-[98vw] overflow-x-scroll flex space-x-3 mb-2 p-2 justify-around">
          <CustomTabsTrigger value="all">All</CustomTabsTrigger>
          <CustomTabsTrigger value="low-stock">Low Stock</CustomTabsTrigger>
          <CustomTabsTrigger value="out-of-stock">Out of Stock</CustomTabsTrigger>
          <CustomTabsTrigger value="live">Live</CustomTabsTrigger>
          <CustomTabsTrigger value="not-live">Not Live</CustomTabsTrigger>
          <CustomTabsTrigger value="rejected">Rejected</CustomTabsTrigger>
        </TabsList>
        <TabsContent value="all">
          {/* Products Table */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <Table className="text-sm">
              <TableHeader>
                <TableRow>
                  <TableHead >Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Live</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length > 0 ? (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id} className="whitespace-nowrap ">
                        {/* Product Column with Image & Name */}
                        <TableCell className="flex items-center space-x-2 max-w-[300px]">
                            <img
                            src={product.image}
                            alt={product.name}
                            className="w-10 h-10 rounded object-cover"
                            />
                            <p className="truncate max-w-[300px]  text-sm leading-tight">
                            {product.name}
                            </p>
                        </TableCell>

                        {/* Category */}
                        <TableCell className="text-sm text-gray-700">{product.category}</TableCell>

                        {/* Price */}
                        <TableCell className="text-sm font-medium">{product.price}</TableCell>

                        {/* Stock */}
                        <TableCell className="text-sm">{product.stock}</TableCell>

                        {/* Live Status */}
                        <TableCell className="text-center">
                            {product.live ? (
                            <MdOutlineCheckCircle className="text-green-500" size={22} />
                            ) : (
                            <MdOutlineVisibilityOff className="text-red-500" size={22} />
                            )}
                        </TableCell>

                        {/* Actions (Edit + Delete) */}
                        <TableCell className=" space-x-2">
                            <section className="flex items-center gap-4">
                                <Button aria-label="Edit Product" className="w-full bg-primary hover:bg-primary/90 flex transition-colors duration-200">
                                      <AiOutlineEdit className="text-white" size={20} />
                                  </Button>
                                  <Button variant={"destructive"} aria-label="Delete Product">
                                      <AiOutlineDelete className="text-white" size={20} />
                                  </Button>
                            </section>

                        </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No products found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="low-stock">
          {/* Products Table */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <Table className="text-sm">
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Live</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length > 0 ? (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id} className="whitespace-nowrap ">
                    {/* Product Column with Image & Name */}
                    <TableCell className="flex items-center space-x-2 max-w-[300px]">
                        <img
                        src={product.image}
                        alt={product.name}
                        className="w-10 h-10 rounded object-cover"
                        />
                        <span className="truncate max-w-[300px]  text-sm leading-tight">
                        {product.name}
                        </span>
                    </TableCell>

                    {/* Category */}
                    <TableCell className="text-sm text-gray-700">{product.category}</TableCell>

                    {/* Price */}
                    <TableCell className="text-sm font-medium">{product.price}</TableCell>

                    {/* Stock */}
                    <TableCell className="text-sm">{product.stock}</TableCell>

                    {/* Live Status */}
                    <TableCell className="text-center">
                        {product.live ? (
                        <MdOutlineCheckCircle className="text-green-500" size={20} />
                        ) : (
                        <MdOutlineVisibilityOff className="text-gray-400" size={20} />
                        )}
                    </TableCell>

                    {/* Actions (Edit + Delete) */}
                    <TableCell className=" space-x-2">
                        <section className="flex items-center gap-2">
                            <button aria-label="Edit Product" className="h-full">
                                <AiOutlineEdit className="text-gray-500" size={20} />
                            </button>
                            <button aria-label="Delete Product">
                                <AiOutlineDelete className="text-gray-500" size={20} />
                            </button>
                        </section>

                    </TableCell>
                </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No products found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="out-of-stock">
          {/* Products Table */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <Table className="text-sm">
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Live</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length > 0 ? (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id} className="whitespace-nowrap ">
                        {/* Product Column with Image & Name */}
                        <TableCell className="flex items-center space-x-2 max-w-[300px]">
                            <img
                            src={product.image}
                            alt={product.name}
                            className="w-10 h-10 rounded object-cover"
                            />
                            <span className="truncate max-w-[300px] text-sm leading-tight">
                            {product.name}
                            </span>
                        </TableCell>

                        {/* Category */}
                        <TableCell className="text-sm text-gray-700">{product.category}</TableCell>

                        {/* Price */}
                        <TableCell className="text-sm font-medium">{product.price}</TableCell>

                        {/* Stock */}
                        <TableCell className="text-sm">{product.stock}</TableCell>

                        {/* Live Status */}
                        <TableCell className="text-center">
                            {product.live ? (
                            <MdOutlineCheckCircle className="text-green-500" size={20} />
                            ) : (
                            <MdOutlineVisibilityOff className="text-gray-400" size={20} />
                            )}
                        </TableCell>

                        {/* Actions (Edit + Delete) */}
                        <TableCell className=" space-x-2">
                            <section className="flex items-center gap-2">
                                <button aria-label="Edit Product" className="h-full">
                                    <AiOutlineEdit className="text-gray-500" size={20} />
                                </button>
                                <button aria-label="Delete Product">
                                    <AiOutlineDelete className="text-gray-500" size={20} />
                                </button>
                            </section>

                        </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No products found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="rejected">
          {/* Products Table */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <Table className="text-sm">
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Live</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length > 0 ? (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id} className="whitespace-nowrap ">
                    {/* Product Column with Image & Name */}
                    <TableCell className="flex items-center space-x-2 max-w-[300px]">
                        <img
                        src={product.image}
                        alt={product.name}
                        className="w-10 h-10 rounded object-cover"
                        />
                        <span className="truncate max-w-[300px] text-sm leading-tight">
                        {product.name}
                        </span>
                    </TableCell>

                    {/* Category */}
                    <TableCell className="text-sm text-gray-700">{product.category}</TableCell>

                    {/* Price */}
                    <TableCell className="text-sm font-medium">{product.price}</TableCell>

                    {/* Stock */}
                    <TableCell className="text-sm">{product.stock}</TableCell>

                    {/* Live Status */}
                    <TableCell className="text-center">
                        {product.live ? (
                        <MdOutlineCheckCircle className="text-green-500" size={20} />
                        ) : (
                        <MdOutlineVisibilityOff className="text-gray-400" size={20} />
                        )}
                    </TableCell>

                    {/* Actions (Edit + Delete) */}
                    <TableCell className=" space-x-2">
                        <section className="flex items-center gap-2">
                            <button aria-label="Edit Product" className="h-full">
                                <AiOutlineEdit className="text-gray-500" size={20} />
                            </button>
                            <button aria-label="Delete Product">
                                <AiOutlineDelete className="text-gray-500" size={20} />
                            </button>
                        </section>

                    </TableCell>
                </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No products found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="live">
          {/* Products Table */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <Table className="text-sm">
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Live</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length > 0 ? (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id} className="whitespace-nowrap ">
                        {/* Product Column with Image & Name */}
                        <TableCell className="flex items-center space-x-2 max-w-[300px]">
                            <img
                            src={product.image}
                            alt={product.name}
                            className="w-10 h-10 rounded object-cover"
                            />
                            <span className="truncate max-w-[300px] text-sm leading-tight">
                            {product.name}
                            </span>
                        </TableCell>

                        {/* Category */}
                        <TableCell className="text-sm text-gray-700">{product.category}</TableCell>

                        {/* Price */}
                        <TableCell className="text-sm font-medium">{product.price}</TableCell>

                        {/* Stock */}
                        <TableCell className="text-sm">{product.stock}</TableCell>

                        {/* Live Status */}
                        <TableCell className="text-center">
                            {product.live ? (
                            <MdOutlineCheckCircle className="text-green-500" size={20} />
                            ) : (
                            <MdOutlineVisibilityOff className="text-gray-400" size={20} />
                            )}
                        </TableCell>

                        {/* Actions (Edit + Delete) */}
                        <TableCell className=" space-x-2">
                            <section className="flex items-center gap-2">
                                <button aria-label="Edit Product" className="h-full">
                                    <AiOutlineEdit className="text-gray-500" size={20} />
                                </button>
                                <button aria-label="Delete Product">
                                    <AiOutlineDelete className="text-gray-500" size={20} />
                                </button>
                            </section>

                        </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No products found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="not-live">
          {/* Products Table */}
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <Table className="text-sm">
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Stock</TableHead>
                  <TableHead>Live</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length > 0 ? (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id} className="whitespace-nowrap ">
                        {/* Product Column with Image & Name */}
                        <TableCell className="flex items-center space-x-2 max-w-[300px]">
                            <img
                            src={product.image}
                            alt={product.name}
                            className="w-10 h-10 rounded object-cover"
                            />
                            <span className="truncate max-w-[300px] text-sm leading-tight">
                            {product.name}
                            </span>
                        </TableCell>

                        {/* Category */}
                        <TableCell className="text-sm text-gray-700">{product.category}</TableCell>

                        {/* Price */}
                        <TableCell className="text-sm font-medium">{product.price}</TableCell>

                        {/* Stock */}
                        <TableCell className="text-sm">{product.stock}</TableCell>

                        {/* Live Status */}
                        <TableCell className="text-center">
                            {product.live ? (
                            <MdOutlineCheckCircle className="text-green-500" size={20} />
                            ) : (
                            <MdOutlineVisibilityOff className="text-gray-400" size={20} />
                            )}
                        </TableCell>

                        {/* Actions (Edit + Delete) */}
                        <TableCell className=" space-x-2">
                            <section className="flex items-center gap-2">
                                <button aria-label="Edit Product" className="h-full">
                                    <AiOutlineEdit className="text-gray-500" size={20} />
                                </button>
                                <button aria-label="Delete Product">
                                    <AiOutlineDelete className="text-gray-500" size={20} />
                                </button>
                            </section>

                        </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No products found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>


      <PaginationComponent
          totalPages={totalPages}
        />

      </section>
    </VerificationGuard>
  );
};

export default FashionBalesPage;
