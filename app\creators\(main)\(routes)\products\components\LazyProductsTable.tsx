"use client";

import React, { lazy, Suspense } from 'react';
import <PERSON>tieLoader from '@/components/ui/LottieLoader';

// Lazy load the heavy table component
const ProductsTable = lazy(() => import('./ProductsTable'));

interface LazyProductsTableProps {
  products: any[];
  isLoading: boolean;
  onViewDetails: (id: string) => void;
  onDeleteProduct: (id: string) => void;
}

const LazyProductsTable: React.FC<LazyProductsTableProps> = (props) => {
  return (
    <Suspense 
      fallback={
        <div className="w-full py-8 flex items-center justify-center">
          <LottieLoader
            size="lg"
            text="Loading products..."
            textSize="md"
            centered={true}
          />
        </div>
      }
    >
      <ProductsTable {...props} />
    </Suspense>
  );
};

export default LazyProductsTable;
