"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

import { ChevronRight, Search, ArrowLeft, X } from "lucide-react"
import { useCategoriesHierarchy } from "@/lib/hooks/use-categories"
import { Category } from "@/lib/types/categories"

interface MultiCategorySelectorProps {
  selectedCategories?: string[]
  onCategoriesChange: (categoryIds: string[]) => void
  placeholder?: string
  className?: string
  error?: boolean
  disabled?: boolean
  maxSelections?: number
}

interface FlattenedCategory {
  _id: string
  name: string
  path: string
}

export function MultiCategorySelector({
  selectedCategories = [],
  onCategoriesChange,
  placeholder = "Select Categories",
  className = "",
  error = false,
  disabled = false,
  maxSelections = 10
}: MultiCategorySelectorProps) {
  const { categories, isLoading: categoriesLoading } = useCategoriesHierarchy()
  
  // Modal state
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const [currentCategories, setCurrentCategories] = React.useState<Category[]>([])
  const [breadcrumb, setBreadcrumb] = React.useState<string[]>([])
  const [searchTerm, setSearchTerm] = React.useState("")

  // Flatten categories for search functionality
  const flattenCategories = (categories: Category[], path: string[] = []): FlattenedCategory[] => {
    return categories.flatMap((category) => {
      const currentPath = [...path, category.name]

      const result: FlattenedCategory[] = []
      
      // Always include the current category
      result.push({
        _id: category._id,
        name: category.name,
        path: currentPath.join(" > ")
      })

      // If it has children, recursively flatten them
      if (category.immediateChildren && category.immediateChildren.length > 0) {
        result.push(...flattenCategories(category.immediateChildren, currentPath))
      }

      return result
    })
  }

  const allCategories = categories ? flattenCategories(categories) : []

  // Get selected category names for display
  const getSelectedCategoryNames = () => {
    if (selectedCategories.length === 0) return placeholder
    
    const selectedNames = selectedCategories
      .map(id => allCategories.find(cat => cat._id === id)?.name)
      .filter(Boolean)
    
    if (selectedNames.length === 0) return placeholder
    if (selectedNames.length === 1) return selectedNames[0]
    return `${selectedNames.length} categories selected`
  }

  // Initialize categories when they load
  React.useEffect(() => {
    if (categories && categories.length > 0 && currentCategories.length === 0) {
      setCurrentCategories(categories)
    }
  }, [categories, currentCategories.length])

  // Filter categories based on search term
  const filteredCategories = React.useMemo(() => {
    if (!searchTerm) {
      return currentCategories
    }
    
    // When searching, show flattened results
    return allCategories.filter(cat =>
      cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cat.path.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, currentCategories, allCategories])

  const openCategoryModal = () => {
    if (disabled) return
    setSearchTerm("")
    setCurrentCategories(categories || [])
    setBreadcrumb([])
    setIsModalOpen(true)
  }

  const toggleCategory = (categoryId: string) => {
    const isSelected = selectedCategories.includes(categoryId)
    
    if (isSelected) {
      // Remove category
      onCategoriesChange(selectedCategories.filter(id => id !== categoryId))
    } else {
      // Add category (check max limit)
      if (selectedCategories.length < maxSelections) {
        onCategoriesChange([...selectedCategories, categoryId])
      }
    }
  }

  const removeCategory = (categoryId: string) => {
    onCategoriesChange(selectedCategories.filter(id => id !== categoryId))
  }

  const goDeeper = (category: Category) => {
    setBreadcrumb(prev => [...prev, category.name])
    setCurrentCategories(category.immediateChildren || [])
    setSearchTerm("") // Clear search when navigating
  }

  const goBack = () => {
    if (breadcrumb.length === 0) return
    
    const newBreadcrumb = breadcrumb.slice(0, -1)
    setBreadcrumb(newBreadcrumb)
    
    // Navigate back through the tree
    let currentLevel = categories || []
    for (const crumb of newBreadcrumb) {
      const category = currentLevel.find(cat => cat.name === crumb)
      if (category && category.immediateChildren) {
        currentLevel = category.immediateChildren
      }
    }
    setCurrentCategories(currentLevel)
  }

  const isSearching = searchTerm.length > 0

  return (
    <>
      <div className={`space-y-2 ${className}`}>
        <Button
          type="button"
          variant="outline"
          className={`w-full justify-start ${error ? 'border-red-300' : ''}`}
          onClick={openCategoryModal}
          disabled={disabled}
        >
          {getSelectedCategoryNames()}
        </Button>

        {/* Selected categories display */}
        {selectedCategories.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {selectedCategories.map(categoryId => {
              const category = allCategories.find(cat => cat._id === categoryId)
              return category ? (
                <Badge key={categoryId} variant="secondary" className="text-xs">
                  {category.name}
                  <X 
                    className="w-3 h-3 ml-1 cursor-pointer" 
                    onClick={() => removeCategory(categoryId)}
                  />
                </Badge>
              ) : null
            })}
          </div>
        )}
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="w-full max-w-[90%] max-h-[90vh] overflow-y-auto rounded-lg">
          <DialogHeader>
            <DialogTitle>Select Categories</DialogTitle>
            <DialogDescription>
              Choose multiple categories for filtering. You can select up to {maxSelections} categories.
            </DialogDescription>
          </DialogHeader>

          {categoriesLoading ? (
            <div className="text-center py-4">Loading categories...</div>
          ) : (
            <>
              {/* Selection counter */}
              <div className="text-sm text-gray-600 mb-2">
                {selectedCategories.length} of {maxSelections} categories selected
              </div>

              {/* Breadcrumb navigation */}
              {breadcrumb.length > 0 && !isSearching && (
                <div className="mb-4">
                  <Button variant="ghost" size="sm" onClick={goBack} className="mb-2">
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    Back
                  </Button>
                </div>
              )}

              {/* Search input */}
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Categories list */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {isSearching ? (
                  // Search results view
                  filteredCategories.length > 0 ? (
                    (filteredCategories as FlattenedCategory[]).map((cat, index) => (
                      <div key={`${cat._id}-${index}`} className="flex items-center space-x-2 p-2 border rounded">
                        <Checkbox
                          checked={selectedCategories.includes(cat._id)}
                          onCheckedChange={() => toggleCategory(cat._id)}
                          disabled={!selectedCategories.includes(cat._id) && selectedCategories.length >= maxSelections}
                        />
                        <div className="flex flex-col flex-1 min-w-0">
                          <div className="font-medium truncate">{cat.name}</div>
                          <div className="text-xs text-gray-500 truncate">{cat.path}</div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      No categories found matching "{searchTerm}"
                    </div>
                  )
                ) : (
                  // Tree navigation view
                  currentCategories.map((cat, index) => (
                    <div key={`${cat._id}-${index}`} className="space-y-1">
                      {cat.immediateChildren && cat.immediateChildren.length > 0 ? (
                        // Category with subcategories
                        <div className="border rounded-lg p-2 bg-gray-50">
                          <div className="flex items-center space-x-2 mb-2">
                            <Checkbox
                              checked={selectedCategories.includes(cat._id)}
                              onCheckedChange={() => toggleCategory(cat._id)}
                              disabled={!selectedCategories.includes(cat._id) && selectedCategories.length >= maxSelections}
                            />
                            <div className="flex flex-col min-w-0 flex-1">
                              <div className="font-medium truncate">{cat.description}</div>
                              <div className="text-xs text-gray-500 whitespace-nowrap">
                                {cat.childrenCount} subcategories
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => goDeeper(cat)}
                          >
                            Browse subcategories
                            <ChevronRight className="w-4 h-4 ml-1" />
                          </Button>
                        </div>
                      ) : (
                        // Leaf category
                        <div className="flex items-center space-x-2 p-2 border rounded">
                          <Checkbox
                            checked={selectedCategories.includes(cat._id)}
                            onCheckedChange={() => toggleCategory(cat._id)}
                            disabled={!selectedCategories.includes(cat._id) && selectedCategories.length >= maxSelections}
                          />
                          <div className="flex flex-col flex-1 min-w-0">
                            <div className="font-medium truncate">{cat.description}</div>
                            <div className="text-xs text-green-600">✓ Final category</div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>

              {/* Action buttons */}
              <div className="flex justify-between mt-4 pt-4 border-t">
                <Button variant="outline" onClick={() => onCategoriesChange([])}>
                  Clear All
                </Button>
                <Button onClick={() => setIsModalOpen(false)}>
                  Done ({selectedCategories.length} selected)
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
