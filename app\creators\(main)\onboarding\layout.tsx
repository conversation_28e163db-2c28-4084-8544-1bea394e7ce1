import type { <PERSON>ada<PERSON> } from "next";
import { Toaster } from "@/components/ui/toaster"
import { CreatorGuard } from "@/lib/components/AuthGuard";
import { StatusProvider } from "@/lib/providers/status-provider";

export const metadata: Metadata = {
  title: "Creator Onboarding - Everyfash",
  description: "Complete your creator profile setup",
};

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <CreatorGuard>
      <StatusProvider>
        <main className="w-full min-h-screen">
          {children}
          <Toaster />
        </main>
      </StatusProvider>
    </CreatorGuard>
  );
}
