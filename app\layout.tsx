import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON>, Plus_Jakarta_Sans } from "next/font/google";
import "./globals.css";
import localFont from "next/font/local";
import { QueryProvider } from "@/lib/providers/query-provider";
import { LoadingProvider } from "@/lib/providers/loading-provider";
import { AuthInitializer } from "@/lib/components/AuthInitializer";
import { Toaster } from "@/components/ui/toaster";
import ServiceWorkerRegistration from "@/components/ServiceWorkerRegistration";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});


const plusJakartaSans = Plus_Jakarta_Sans({
  variable: "--font-plus-jakarta-sans",
  subsets: ["latin"],
});

const AvantGarde = localFont({
  variable: "--font-avant-garde",
  src: "../public/fonts/avant-garde.woff2",
});

export const metadata: Metadata = {
  title: "Everyfash",
  description: "Largest Fashion Marketplace",
};

// ${geistSans.variable} ${geistMono.variable}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${plusJakartaSans.variable} ${AvantGarde.variable} antialiased`}
      >
        <QueryProvider>
          <LoadingProvider>
            <AuthInitializer>
              {children}
              <Toaster />
              <ServiceWorkerRegistration />
            </AuthInitializer>
          </LoadingProvider>
        </QueryProvider>
      </body>
    </html>
  );
}


