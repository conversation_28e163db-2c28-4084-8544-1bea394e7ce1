/**
 * Authentication Manager
 * Centralized authentication token handling and session management
 */

import { AUTH_REDIRECTS } from './config';

export interface TokenInfo {
  token: string;
  expiresAt: number;
  issuedAt: number;
  role: 'buyer' | 'creator';
}

export interface SessionInfo {
  isValid: boolean;
  isExpired: boolean;
  expiresIn: number; // seconds until expiration
  role?: 'buyer' | 'creator';
}

export class AuthManager {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly TOKEN_INFO_KEY = 'auth_token_info';
  private static readonly REFRESH_THRESHOLD = 60 * 60 * 1000; // 5 minutes in milliseconds

  /**
   * Store authentication token with metadata
   */
  static setToken(token: string, role: 'buyer' | 'creator'): void {
    if (typeof window === 'undefined') return;

    try {
      const tokenInfo = this.parseToken(token);
      const tokenData: TokenInfo = {
        token,
        expiresAt: tokenInfo.exp * 1000, // Convert to milliseconds
        issuedAt: tokenInfo.iat * 1000,
        role,
      };

      localStorage.setItem(this.TOKEN_KEY, token);
      localStorage.setItem(this.TOKEN_INFO_KEY, JSON.stringify(tokenData));
    } catch (error) {
      console.error('Failed to store token:', error);
      // Store token without metadata as fallback
      localStorage.setItem(this.TOKEN_KEY, token);
    }
  }

  /**
   * Get current authentication token
   */
  static getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Get token information
   */
  static getTokenInfo(): TokenInfo | null {
    if (typeof window === 'undefined') return null;

    try {
      const tokenInfoStr = localStorage.getItem(this.TOKEN_INFO_KEY);
      if (!tokenInfoStr) return null;

      return JSON.parse(tokenInfoStr) as TokenInfo;
    } catch (error) {
      console.error('Failed to parse token info:', error);
      return null;
    }
  }

  /**
   * Get current session information
   */
  static getSessionInfo(): SessionInfo {
    const token = this.getToken();
    const tokenInfo = this.getTokenInfo();

    if (!token) {
      return {
        isValid: false,
        isExpired: true,
        expiresIn: 0,
      };
    }

    if (!tokenInfo) {
      // Fallback: try to parse token directly
      try {
        const parsed = this.parseToken(token);
        const expiresAt = parsed.exp * 1000;
        const now = Date.now();
        
        return {
          isValid: expiresAt > now,
          isExpired: expiresAt <= now,
          expiresIn: Math.max(0, Math.floor((expiresAt - now) / 1000)),
        };
      } catch (error) {
        return {
          isValid: false,
          isExpired: true,
          expiresIn: 0,
        };
      }
    }

    const now = Date.now();
    const isExpired = tokenInfo.expiresAt <= now;
    const expiresIn = Math.max(0, Math.floor((tokenInfo.expiresAt - now) / 1000));

    return {
      isValid: !isExpired,
      isExpired,
      expiresIn,
      role: tokenInfo.role,
    };
  }

  /**
   * Check if token needs refresh (within threshold of expiration)
   */
  static needsRefresh(): boolean {
    const tokenInfo = this.getTokenInfo();
    if (!tokenInfo) return false;

    const now = Date.now();
    const timeUntilExpiry = tokenInfo.expiresAt - now;
    
    return timeUntilExpiry <= this.REFRESH_THRESHOLD && timeUntilExpiry > 0;
  }

  /**
   * Clear authentication data
   */
  static clearAuth(): void {
    if (typeof window === 'undefined') return;

    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.TOKEN_INFO_KEY);
  }

  /**
   * Handle authentication failure (redirect to appropriate login)
   */
  static handleAuthFailure(currentPath?: string): void {
    this.clearAuth();

    if (typeof window === 'undefined') return;

    const path = currentPath || window.location.pathname;

    // Don't redirect if we're already on a login page
    if (path.includes('/login') || path.includes('/register')) {
      console.log('AuthManager - Already on auth page, skipping redirect');
      return;
    }

    const redirectUrl = path.startsWith('/creators')
      ? AUTH_REDIRECTS.CREATOR_LOGIN
      : AUTH_REDIRECTS.BUYER_LOGIN;

    // Add return URL for post-login redirect
    const returnUrl = encodeURIComponent(path);
    const finalUrl = `${redirectUrl}?returnUrl=${returnUrl}`;

    console.log('AuthManager - Redirecting to:', finalUrl);
    window.location.href = finalUrl;
  }

  /**
   * Get appropriate dashboard URL for user role
   */
  static getDashboardUrl(role?: 'buyer' | 'creator'): string {
    const sessionInfo = this.getSessionInfo();
    const userRole = role || sessionInfo.role;

    return userRole === 'creator' 
      ? AUTH_REDIRECTS.CREATOR_DASHBOARD 
      : AUTH_REDIRECTS.BUYER_DASHBOARD;
  }

  /**
   * Parse JWT token payload
   */
  private static parseToken(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = atob(payload);
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Invalid token format');
    }
  }

  /**
   * Validate token format
   */
  static isValidTokenFormat(token: string): boolean {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      // Try to decode each part
      atob(parts[0]); // header
      atob(parts[1]); // payload
      // signature is not base64 decoded

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get authorization header value
   */
  static getAuthHeader(): string | null {
    const token = this.getToken();
    return token ? `Bearer ${token}` : null;
  }

  /**
   * Check if user has specific role
   */
  static hasRole(role: 'buyer' | 'creator'): boolean {
    const sessionInfo = this.getSessionInfo();
    return sessionInfo.isValid && sessionInfo.role === role;
  }

  /**
   * Auto-refresh token if needed (placeholder for future implementation)
   */
  static async refreshTokenIfNeeded(): Promise<boolean> {
    // This would be implemented when refresh token functionality is added
    // For now, just check if current token is still valid
    const sessionInfo = this.getSessionInfo();
    return sessionInfo.isValid;
  }

  /**
   * Schedule automatic token refresh (placeholder for future implementation)
   */
  static scheduleTokenRefresh(): void {
    // This would set up automatic token refresh before expiration
    // Implementation would depend on refresh token strategy
  }
}

// Utility functions for backward compatibility
export const getAuthToken = (): string | null => {
  return AuthManager.getToken();
};

export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000 < Date.now();
  } catch {
    return true;
  }
};
