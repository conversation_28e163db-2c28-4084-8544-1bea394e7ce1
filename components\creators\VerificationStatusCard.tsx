'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertCircle, Clock, CheckCircle, XCircle, FileText, Mail, Calendar, Shield, User, ArrowRight } from 'lucide-react';
import { VerificationStatusData } from '@/lib/types/auth';

interface VerificationStatusCardProps {
  verificationData: VerificationStatusData;
  onViewDocuments?: () => void;
}

const VerificationStatusCard = ({ verificationData, onViewDocuments }: VerificationStatusCardProps) => {
  const { verificationStatus, statusMessage, nextSteps, verificationDetails, creatorInfo } = verificationData;

  const getStatusConfig = () => {
    switch (verificationStatus) {
      case 'verified':
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-600" />,
          badge: <Badge className="bg-green-100 text-green-700 text-xs">Verified</Badge>,
          iconBg: 'bg-green-50'
        };
      case 'rejected':
        return {
          icon: <XCircle className="h-5 w-5 text-red-600" />,
          badge: <Badge variant="destructive" className="text-xs">Rejected</Badge>,
          iconBg: 'bg-red-50'
        };
      case 'pending':
        return {
          icon: <Clock className="h-5 w-5 text-amber-600" />,
          badge: <Badge className="bg-amber-100 text-amber-700 text-xs">Under Review</Badge>,
          iconBg: 'bg-amber-50'
        };
      case 'unverified':
        return {
          icon: <Clock className="h-5 w-5 text-amber-600" />,
          badge: <Badge className="bg-amber-100 text-amber-700 text-xs">Under Review</Badge>,
          iconBg: 'bg-amber-50'
        };
      case 'not_submitted':
        return {
          icon: <Shield className="h-5 w-5 text-gray-600" />,
          badge: <Badge variant="secondary" className="text-xs">Not Submitted</Badge>,
          iconBg: 'bg-gray-50'
        };
      default:
        return {
          icon: <AlertCircle className="h-5 w-5 text-gray-600" />,
          badge: <Badge variant="secondary" className="text-xs">Unknown</Badge>,
          iconBg: 'bg-gray-50'
        };
    }
  };

  const isVerified = verificationStatus === 'verified';
  const isUnverified = verificationStatus === 'unverified';
  const isPending = verificationStatus === 'pending';
  const isRejected = verificationStatus === 'rejected';
  const isNotSubmitted = verificationStatus === 'not_submitted';

  const statusConfig = getStatusConfig();

  return (
    <Card className="bg-white border border-gray-200 shadow-sm">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Left side - Status info */}
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${statusConfig.iconBg}`}>
              {statusConfig.icon}
            </div>
            <div>
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="text-sm font-semibold text-gray-900">Account Verification</h3>
                {statusConfig.badge}
              </div>
              <p className="text-xs text-gray-600">{statusMessage}</p>
            </div>
          </div>

          {/* Right side - Action button (only for actionable states) */}
          {(isRejected || isNotSubmitted) && (
            <Button size="sm" className="text-xs">
              {isRejected ? 'Resubmit' : 'Verify Now'}
            </Button>
          )}
        </div>

        {/* Minimal restriction notice for unverified users */}
        {!isVerified && (
          <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="text-xs text-amber-800">
              {isRejected && "Verification rejected - resubmit documents to continue"}
              {isNotSubmitted && "Verification required to access all features"}
              {isPending && "Account under review - limited access until verified"}
              {isUnverified && "Verification required to access all features"}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VerificationStatusCard;
