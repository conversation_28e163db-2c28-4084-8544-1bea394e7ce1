"use client";

import { useEffect } from 'react';
import { registerServiceWorker, showUpdateAvailable } from '@/lib/utils/service-worker';

const ServiceWorkerRegistration = () => {
  useEffect(() => {
    // Register service worker
    registerServiceWorker({
      onUpdate: () => {
        showUpdateAvailable();
      },
      onSuccess: () => {
        console.log('App is ready for offline use');
      },
      onError: (error) => {
        console.error('Service worker registration failed:', error);
      },
    });
  }, []);

  return null; // This component doesn't render anything
};

export default ServiceWorkerRegistration;
