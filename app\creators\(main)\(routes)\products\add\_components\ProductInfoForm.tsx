"use client"

import * as React from "react"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { X, Upload, Plus, GripVertical } from "lucide-react"
import { useProductForm } from "@/lib/contexts/ProductFormContext"
import { GENDER_OPTIONS, ProductInfoFormData as ProductInfoType, BaleInfoFormData } from "@/lib/types/products"
import { toast } from "@/hooks/use-toast"
import { CategorySelector } from "@/lib/components/CategorySelector"
import BaleInfoForm from "./BaleInfoForm"

// Form validation schema
const productInfoSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  brand: z.string().min(1, "Brand is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  basePrice: z.coerce.number().min(0.01, "Base price must be greater than 0"),
  category: z.string().min(1, "Category is required"),
  gender: z.enum(['Male', 'Female', 'Unisex'], {
    required_error: "Please select a gender",
  }),
  highlights: z.array(z.string()).min(1, "At least one highlight is required"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
})

type ProductInfoFormData = z.infer<typeof productInfoSchema>

interface ProductInfoFormProps {
  onNext: () => void;
}

interface ProductInfoFormProps {
  onNext: () => void;
  onBack?: () => void;
}

export default function ProductInfoForm({ onNext, onBack }: ProductInfoFormProps) {
  const { currentType, productInfo, updateProductInfo } = useProductForm()

  
  // Local state for UI
  const [productImages, setProductImages] = React.useState<File[]>(productInfo.productImages || [])
  const [imagePreviewUrls, setImagePreviewUrls] = React.useState<string[]>([])

  const [newHighlight, setNewHighlight] = React.useState("")
  const [newTag, setNewTag] = React.useState("")
  const [draggedIndex, setDraggedIndex] = React.useState<number | null>(null)


  
  // Form setup - only handle products for now, bales will be handled separately
  const form = useForm<ProductInfoFormData>({
    resolver: zodResolver(productInfoSchema),
    defaultValues: {
      name: productInfo.name || "",
      brand: currentType === 'product' ? (productInfo as ProductInfoType).brand || "" : "",
      description: productInfo.description || "",
      basePrice: productInfo.basePrice || 0,
      category: currentType === 'product' ? (productInfo as ProductInfoType).category || "" : "",
      gender: currentType === 'product' ? (productInfo as ProductInfoType).gender || "Unisex" : "Unisex",
      highlights: productInfo.highlights || [],
      tags: productInfo.tags || [],
    },
  })

  // If current type is bale, use the BaleInfoForm
  if (currentType === 'bale') {
    return <BaleInfoForm onNext={onNext} onBack={onBack} />;
  }

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form

  // Initialize image previews from existing context data
  React.useEffect(() => {
    if (productInfo.productImages && productInfo.productImages.length > 0) {
      setProductImages(productInfo.productImages)
      // Create preview URLs for existing files
      const previews = productInfo.productImages.map(file => URL.createObjectURL(file))
      setImagePreviewUrls(previews)
    }
  }, [productInfo.productImages])

  // Handle image uploads
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    // Limit to 5 images total
    const remainingSlots = 5 - productImages.length
    const filesToAdd = files.slice(0, remainingSlots)

    if (filesToAdd.length < files.length) {
      toast({
        variant: "destructive",
        title: "Too many images",
        description: "You can only upload up to 5 images total.",
      })
    }

    // Create preview URLs
    const newPreviewUrls = filesToAdd.map(file => URL.createObjectURL(file))
    
    // Update state
    const updatedImages = [...productImages, ...filesToAdd]
    const updatedPreviews = [...imagePreviewUrls, ...newPreviewUrls]
    
    setProductImages(updatedImages)
    setImagePreviewUrls(updatedPreviews)
  }

  const removeImage = (index: number) => {
    const updatedImages = productImages.filter((_, i) => i !== index)
    const updatedPreviews = imagePreviewUrls.filter((_, i) => i !== index)

    // Revoke the URL to prevent memory leaks
    if (imagePreviewUrls[index]) {
      URL.revokeObjectURL(imagePreviewUrls[index])
    }

    setProductImages(updatedImages)
    setImagePreviewUrls(updatedPreviews)
  }

  // Drag and drop functions for rearranging images
  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null)
      return
    }

    const newImages = [...productImages]
    const newPreviews = [...imagePreviewUrls]

    // Move the dragged items
    const draggedImage = newImages[draggedIndex]
    const draggedPreview = newPreviews[draggedIndex]

    // Remove from original position
    newImages.splice(draggedIndex, 1)
    newPreviews.splice(draggedIndex, 1)

    // Insert at new position
    newImages.splice(dropIndex, 0, draggedImage)
    newPreviews.splice(dropIndex, 0, draggedPreview)

    setProductImages(newImages)
    setImagePreviewUrls(newPreviews)
    setDraggedIndex(null)
  }

  // Handle highlights
  const addHighlight = () => {
    if (newHighlight.trim()) {
      const currentHighlights = watch('highlights') || []
      setValue('highlights', [...currentHighlights, newHighlight.trim()])
      setNewHighlight("")
    }
  }

  const removeHighlight = (index: number) => {
    const currentHighlights = watch('highlights') || []
    setValue('highlights', currentHighlights.filter((_, i) => i !== index))
  }

  // Handle tags
  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = watch('tags') || []
      setValue('tags', [...currentTags, newTag.trim()])
      setNewTag("")
    }
  }

  const removeTag = (index: number) => {
    const currentTags = watch('tags') || []
    setValue('tags', currentTags.filter((_, i) => i !== index))
  }

  // Category navigation types and functions


  const onSubmit = (data: ProductInfoFormData) => {
    console.log("Form data being submitted:", data)
    console.log("Product images:", productImages)

    // Validate images
    if (productImages.length === 0) {
      toast({
        variant: "destructive",
        title: "Images Required",
        description: "Please upload at least one product image.",
      })
      return
    }

    // Validate required fields
    if (!data.name?.trim()) {
      toast({
        variant: "destructive",
        title: "Name Required",
        description: "Please enter a product name.",
      })
      return
    }

    if (!data.brand?.trim()) {
      toast({
        variant: "destructive",
        title: "Brand Required",
        description: "Please enter a brand name.",
      })
      return
    }

    if (!data.category) {
      toast({
        variant: "destructive",
        title: "Category Required",
        description: "Please select a category.",
      })
      return
    }

    // Update context with final data
    const formDataToSave = {
      ...data,
      productImages,
      // Ensure all required fields have values
      name: data.name.trim(),
      brand: data.brand.trim(),
      description: data.description.trim(),
      highlights: data.highlights.filter(h => h.trim()),
      tags: data.tags.filter(t => t.trim()),
    }

    console.log("Saving to context:", formDataToSave)
    updateProductInfo(formDataToSave)

    // Navigate to next step
    onNext()
  }

  return (
    <div className="w-full mt-4">
      <form onSubmit={handleSubmit(onSubmit)} className="w-full p-4 rounded-lg bg-white mb-4">
        
        {/* Image upload section */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-2">
            Product Images <span className="text-red-500">*</span>
          </label>
          <p className="text-xs text-gray-500 mb-3">Upload up to 5 images. First image will be the main image.</p>
          
          {/* Image grid */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
            {imagePreviewUrls.map((url, index) => (
              <div
                key={index}
                className={`relative border rounded-lg h-24 bg-gray-100 cursor-move transition-all ${
                  draggedIndex === index ? 'opacity-50 scale-95' : 'hover:shadow-md'
                }`}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, index)}
              >
                <img src={url} alt={`Product ${index + 1}`} className="w-full h-full object-cover rounded-lg" />

                {/* Drag handle */}
                <div className="absolute top-1 left-1 bg-black bg-opacity-50 text-white rounded p-1">
                  <GripVertical className="w-3 h-3" />
                </div>

                {/* Remove button */}
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 text-xs hover:bg-red-600"
                >
                  <X className="w-3 h-3" />
                </button>

                {/* Main image indicator */}
                {index === 0 && (
                  <div className="absolute bottom-1 left-1 bg-blue-500 text-white text-xs px-1 rounded">
                    Main
                  </div>
                )}

                {/* Image order indicator */}
                <div className="absolute bottom-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                  {index + 1}
                </div>
              </div>
            ))}
            
            {/* Add image button */}
            {productImages.length < 5 && (
              <label className="border-2 border-dashed border-gray-300 rounded-lg h-24 flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors">
                <Upload className="w-6 h-6 text-gray-400 mb-1" />
                <span className="text-xs text-gray-500">Add Image</span>
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageUpload}
                />
              </label>
            )}
          </div>
        </div>

        {/* Product name */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Product Name <span className="text-red-500">*</span>
          </label>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Enter product name"
                className={errors.name ? 'border-red-300' : ''}
              />
            )}
          />
          {errors.name && (
            <p className="text-xs text-red-600 mt-1">{errors.name.message}</p>
          )}
        </div>

        {/* Brand and Gender */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Brand <span className="text-red-500">*</span>
            </label>
            <Controller
              name="brand"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="Enter brand name"
                  className={errors.brand ? 'border-red-300' : ''}
                />
              )}
            />
            {errors.brand && (
              <p className="text-xs text-red-600 mt-1">{errors.brand.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Gender <span className="text-red-500">*</span>
            </label>
            <Controller
              name="gender"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={errors.gender ? 'border-red-300' : ''}>
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    {GENDER_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.gender && (
              <p className="text-xs text-red-600 mt-1">{errors.gender.message}</p>
            )}
          </div>
        </div>

        {/* Base Price and Category */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Base Price (GHS) <span className="text-red-500">*</span>
            </label>
            <Controller
              name="basePrice"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className={errors.basePrice ? 'border-red-300' : ''}
                />
              )}
            />
            {errors.basePrice && (
              <p className="text-xs text-red-600 mt-1">{errors.basePrice.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Category <span className="text-red-500">*</span>
            </label>
            <Controller
              name="category"
              control={control}
              render={({ field }) => (
                <CategorySelector
                  value={field.value}
                  onValueChange={(categoryId, categoryName) => {
                    field.onChange(categoryId)
                  }}
                  placeholder="Select Category"
                  error={!!errors.category}
                />
              )}
            />
            {errors.category && (
              <p className="text-xs text-red-600 mt-1">{errors.category.message}</p>
            )}
          </div>
        </div>

        {/* Description */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Description <span className="text-red-500">*</span>
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder="Describe your product in detail..."
                rows={4}
                className={errors.description ? 'border-red-300' : ''}
              />
            )}
          />
          {errors.description && (
            <p className="text-xs text-red-600 mt-1">{errors.description.message}</p>
          )}
        </div>

        {/* Highlights */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Product Highlights <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <Input
              value={newHighlight}
              onChange={(e) => setNewHighlight(e.target.value)}
              placeholder="Add a highlight..."
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addHighlight())}
            />
            <Button type="button" onClick={addHighlight} size="sm">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch('highlights')?.map((highlight, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {highlight}
                <button
                  type="button"
                  onClick={() => removeHighlight(index)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          {errors.highlights && (
            <p className="text-xs text-red-600 mt-1">{errors.highlights.message}</p>
          )}
        </div>

        {/* Tags */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Tags <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag..."
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
            />
            <Button type="button" onClick={addTag} size="sm">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch('tags')?.map((tag, index) => (
              <Badge key={index} variant="outline" className="flex items-center gap-1">
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(index)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          {errors.tags && (
            <p className="text-xs text-red-600 mt-1">{errors.tags.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button type="submit" className="px-8">
            Next: Specifications
          </Button>
        </div>
      </form>
    </div>
  )
}
