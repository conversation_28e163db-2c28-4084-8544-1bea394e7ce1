import { useQuery } from '@tanstack/react-query';
import { buyerProductsApi, BuyerProductsQueryParams } from '@/lib/api/buyer-products';
import { useAuth } from '@/lib/stores/auth-store';

// Query keys for buyer products
export const buyerProductKeys = {
  all: ['buyer-products'] as const,
  lists: () => [...buyerProductKeys.all, 'list'] as const,
  list: (params: string) => [...buyerProductKeys.lists(), { params }] as const,
  details: () => [...buyerProductKeys.all, 'detail'] as const,
  detail: (id: string) => [...buyerProductKeys.details(), id] as const,
};

// Hook for fetching products list
export const useBuyerProducts = (
  params?: BuyerProductsQueryParams,
  options?: { enabled?: boolean }
) => {
  const { isAuthenticated } = useAuth();
  
  // Automatically set usePreferences based on authentication status
  const queryParams = {
    ...params,
    usePreferences: isAuthenticated && (params?.usePreferences !== false),
  };

  return useQuery({
    queryKey: buyerProductKeys.list(JSON.stringify(queryParams)),
    queryFn: () => buyerProductsApi.getProducts(queryParams),
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter than creator products since this is more dynamic)
    enabled: options?.enabled !== false,
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors except 408, 429
      const status = error?.status || error?.response?.status;
      if (status >= 400 && status < 500 && status !== 408 && status !== 429) {
        return false;
      }
      return failureCount < 2; // Reduced retries for public endpoint
    },
    refetchOnWindowFocus: false, // Disable refetch on window focus for better UX
  });
};

// Hook for fetching single product details
export const useBuyerProduct = (
  productId: string,
  options?: { enabled?: boolean }
) => {
  return useQuery({
    queryKey: buyerProductKeys.detail(productId),
    queryFn: () => buyerProductsApi.getProduct(productId),
    enabled: !!productId && (options?.enabled !== false),
    staleTime: 5 * 60 * 1000, // 5 minutes for product details
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors except 408, 429
      const status = error?.status || error?.response?.status;
      if (status >= 400 && status < 500 && status !== 408 && status !== 429) {
        return false;
      }
      return failureCount < 2;
    },
    refetchOnWindowFocus: false,
  });
};

// Hook for search functionality with debouncing
export const useBuyerProductsSearch = (
  searchTerm: string,
  additionalParams?: Omit<BuyerProductsQueryParams, 'search'>,
  options?: { enabled?: boolean; debounceMs?: number }
) => {
  const { isAuthenticated } = useAuth();
  
  const queryParams = {
    ...additionalParams,
    search: searchTerm.trim(),
    usePreferences: isAuthenticated && (additionalParams?.usePreferences !== false),
  };

  return useQuery({
    queryKey: buyerProductKeys.list(JSON.stringify(queryParams)),
    queryFn: () => buyerProductsApi.getProducts(queryParams),
    enabled: (options?.enabled !== false) && searchTerm.trim().length >= 2, // Only search with 2+ characters
    staleTime: 1 * 60 * 1000, // 1 minute for search results
    retry: 1, // Minimal retries for search
    refetchOnWindowFocus: false,
  });
};

// Hook for category-specific products
export const useBuyerProductsByCategory = (
  categoryId: string,
  additionalParams?: Omit<BuyerProductsQueryParams, 'categoryId'>,
  options?: { enabled?: boolean }
) => {
  const { isAuthenticated } = useAuth();
  
  const queryParams = {
    ...additionalParams,
    categoryId,
    usePreferences: isAuthenticated && (additionalParams?.usePreferences !== false),
  };

  return useQuery({
    queryKey: buyerProductKeys.list(JSON.stringify(queryParams)),
    queryFn: () => buyerProductsApi.getProducts(queryParams),
    enabled: !!categoryId && (options?.enabled !== false),
    staleTime: 3 * 60 * 1000, // 3 minutes for category results
    retry: 1,
    refetchOnWindowFocus: false,
  });
};
