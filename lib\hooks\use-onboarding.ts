import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { onboardingApi, handleOnboardingApiError } from '@/lib/api';
import {
  BusinessInfoUpdateData,
  PaymentInfoResponse,
  PaymentInfoUpdateData,
  ShopInfoResponse,
  ShopInfoUpdateData,
  ShippingInfoResponse,
  ShippingInfoUpdateData,
  VerificationStatusResponse
} from '@/lib/types/auth';

// Query keys
export const onboardingKeys = {
  all: ['onboarding'] as const,
  status: () => [...onboardingKeys.all, 'status'] as const,
  businessInfo: () => [...onboardingKeys.all, 'business-info'] as const,
  paymentInfo: () => [...onboardingKeys.all, 'payment-info'] as const,
  shopInfo: () => [...onboardingKeys.all, 'shop-info'] as const,
  shippingInfo: () => [...onboardingKeys.all, 'shipping-info'] as const,
  verificationStatus: () => [...onboardingKeys.all, 'verification-status'] as const,
};

// Get onboarding status query
export const useOnboardingStatusQuery = () => {
  return useQuery({
    queryKey: onboardingKeys.status(),
    queryFn: onboardingApi.getOnboardingStatus,
    staleTime: 10 * 60 * 1000, // 10 minutes - much longer cache
    gcTime: 15 * 60 * 1000, // 15 minutes - keep in cache longer
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) errors
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Get business information query
export const useBusinessInfo = () => {
  return useQuery({
    queryKey: onboardingKeys.businessInfo(),
    queryFn: onboardingApi.getBusinessInfo,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) errors
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Update business information mutation
export const useUpdateBusinessInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BusinessInfoUpdateData) => onboardingApi.updateBusinessInfo(data),
    onSuccess: (data) => {
      // Update the cached business info
      queryClient.setQueryData(onboardingKeys.businessInfo(), data);
      
      // Show success message
      toast({
        title: 'Business Information Updated',
        description: 'Your business information has been successfully updated.',
        className: 'bg-green-100 text-green-800',
      });

      // Invalidate and refetch to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: onboardingKeys.businessInfo() });
      queryClient.invalidateQueries({ queryKey: onboardingKeys.status() });
      // Also invalidate verification status as business info affects verification
      queryClient.invalidateQueries({ queryKey: onboardingKeys.verificationStatus() });
    },
    onError: (error) => {
      const apiError = handleOnboardingApiError(error);
      
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: apiError.message,
      });
    },
  });
};

// Get payment information query
export const usePaymentInfo = () => {
  return useQuery({
    queryKey: onboardingKeys.paymentInfo(),
    queryFn: onboardingApi.getPaymentInfo,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) errors
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Update payment information mutation
export const useUpdatePaymentInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: PaymentInfoUpdateData) => onboardingApi.updatePaymentInfo(data),
    onSuccess: (data) => {
      // Update the cached payment info
      queryClient.setQueryData(onboardingKeys.paymentInfo(), data);

      // Show success message
      toast({
        title: 'Payment Information Updated',
        description: 'Your payment information has been successfully updated.',
        className: 'bg-green-100 text-green-800',
      });

      // Invalidate and refetch to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: onboardingKeys.paymentInfo() });
      queryClient.invalidateQueries({ queryKey: onboardingKeys.status() });
    },
    onError: (error) => {
      const apiError = handleOnboardingApiError(error);

      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: apiError.message,
      });
    },
  });
};

// Get shop information query
export const useShopInfo = () => {
  return useQuery({
    queryKey: onboardingKeys.shopInfo(),
    queryFn: onboardingApi.getShopInfo,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) errors
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Update shop information mutation
export const useUpdateShopInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ShopInfoUpdateData) => onboardingApi.updateShopInfo(data),
    onSuccess: (data) => {
      // Update the cached shop info
      queryClient.setQueryData(onboardingKeys.shopInfo(), data);

      // Show success message
      toast({
        title: 'Shop Information Updated',
        description: 'Your shop information has been successfully updated.',
        className: 'bg-green-100 text-green-800',
      });

      // Invalidate and refetch to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: onboardingKeys.shopInfo() });
      queryClient.invalidateQueries({ queryKey: onboardingKeys.status() });
    },
    onError: (error) => {
      const apiError = handleOnboardingApiError(error);

      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description: apiError.message,
      });
    },
  });
};

// Check if user needs onboarding
export const useOnboardingStatus = () => {
  const { data: statusResponse, isLoading, error } = useOnboardingStatusQuery();

  const statusData = statusResponse?.data;
  const progress = statusData?.onboardingProgress;

  const needsOnboarding = () => {
    // STRICT ACCESS CONTROL: Use the explicit isComplete flag from API
    // This is the authoritative source for determining if a creator can access other pages
    if (statusData?.isComplete === true) {
      return false; // Only allow access to other pages if explicitly complete
    }

    // If isComplete is false, undefined, or null, user needs onboarding
    // This ensures no access to creator pages until server confirms completion
    return true;
  };

  const getNextStep = () => {
    if (!progress) {
      return 'business-info';
    }

    if (!progress.businessInfo) return 'business-info';
    if (!progress.paymentInfo) return 'payment-info';
    if (!progress.shopInfo) return 'shop-info';
    if (!progress.shippingInfo) return 'shipping-info';
    return null;
  };

  const getCompletedSteps = () => {
    if (!progress) {
      return 0;
    }

    let completed = 0;
    if (progress.businessInfo) completed++;
    if (progress.paymentInfo) completed++;
    if (progress.shopInfo) completed++;
    if (progress.shippingInfo) completed++;
    return completed;
  };

  return {
    isLoading,
    error,
    needsOnboarding: needsOnboarding(),
    nextStep: getNextStep(),
    completedSteps: getCompletedSteps(),
    totalSteps: 4, // business info, payment info, shop info, shipping info
    progress,
    onboardingStatus: statusData?.onboardingStatus,
    isComplete: statusData?.isComplete,
  };
};

// Get shipping info
export const useShippingInfo = () => {
  return useQuery({
    queryKey: onboardingKeys.shippingInfo(),
    queryFn: onboardingApi.getShippingInfo,
    retry: 1,
  });
};

// Update shipping info
export const useUpdateShippingInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: onboardingApi.updateShippingInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: onboardingKeys.shippingInfo() });
      queryClient.invalidateQueries({ queryKey: onboardingKeys.status() });
    },
    onError: handleOnboardingApiError,
  });
};

// Get verification status
export const useVerificationStatus = () => {
  return useQuery({
    queryKey: onboardingKeys.verificationStatus(),
    queryFn: onboardingApi.getVerificationStatus,
    staleTime: 15 * 60 * 1000, // 15 minutes - longer cache for verification status
    gcTime: 20 * 60 * 1000, // 20 minutes - keep in cache longer
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: false, // Don't refetch on component mount if data exists
    retry: (failureCount, error: any) => {
      // Don't retry on 401 (unauthorized) errors
      if (error?.message?.includes('Session expired')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Utility functions for manual cache management
export const useInvalidateOnboardingCache = () => {
  const queryClient = useQueryClient();

  return {
    // Invalidate all onboarding-related queries
    invalidateAll: () => {
      queryClient.invalidateQueries({ queryKey: onboardingKeys.all });
    },

    // Invalidate specific queries
    invalidateStatus: () => {
      queryClient.invalidateQueries({ queryKey: onboardingKeys.status() });
    },

    invalidateVerificationStatus: () => {
      queryClient.invalidateQueries({ queryKey: onboardingKeys.verificationStatus() });
    },

    // Force refetch (ignores cache)
    refetchStatus: () => {
      queryClient.refetchQueries({ queryKey: onboardingKeys.status() });
    },

    refetchVerificationStatus: () => {
      queryClient.refetchQueries({ queryKey: onboardingKeys.verificationStatus() });
    },

    // Clear cache completely
    clearCache: () => {
      queryClient.removeQueries({ queryKey: onboardingKeys.all });
    },
  };
};
