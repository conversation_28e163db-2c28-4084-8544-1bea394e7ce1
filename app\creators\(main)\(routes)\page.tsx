"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { FaArrowUp, FaArrowDown } from "react-icons/fa";
import Link from "next/link";
import { useVerificationStatusFromContext } from "@/lib/providers/status-provider";
import VerificationStatusCard from "@/components/creators/VerificationStatusCard";
import LottieLoader from "@/components/ui/LottieLoader";


export default function HomePage() {
  type MetricTab = '7d' | '30d' | '90d';
  const [activeTab, setActiveTab] = useState<MetricTab>('7d');

  // Get verification status from context
  const { data: verificationResponse, isLoading: isLoadingVerification, error: verificationError, isVerified } = useVerificationStatusFromContext();
  const verificationData = verificationResponse?.data;

  // Show verification card if not verified or if there's verification data to display
  const shouldShowVerificationCard = verificationData && !isVerified;

  // Disable task cards if not verified or still loading
  const shouldDisableTaskCards = isLoadingVerification || !isVerified;

  const metrics = {
    "7d": { revenue: "GH₵ 20,000", trend: "down", percentage: "-5%" },
    "30d": { revenue: "GH₵ 100,000", trend: "up", percentage: "+12%" },
    "90d": { revenue: "GH₵ 300,000", trend: "up", percentage: "+8%" },
  };


  return (
    <div className="w-full">
      {/* Welcome Section */}
      <h1 className="text-sm text-gray-600 mb-3 mt-4">Hey there! Here’s how <span className="font-semibold">The Thrift Shop</span> is doing.</h1>

      {/* Verification Status Section */}
      {isLoadingVerification ? (
        <div className="flex items-center justify-center p-6 mb-6 bg-white rounded-lg shadow-lg">
          <LottieLoader
            size="md"
            text="Checking verification status..."
            textSize="sm"
            centered={false}
          />
        </div>
      ) : verificationError ? (
        <div className="p-4 mb-6 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">Unable to load verification status. Please try again later.</p>
        </div>
      ) : shouldShowVerificationCard ? (
        <div className="mb-6">
          <VerificationStatusCard verificationData={verificationData} />
        </div>
      ) : null}

      {/* Yours to do Section */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
        <TaskCard
          title="Available Products (0)"
          description={isVerified ? "List products to sell on Everyfash" : "Complete verification to list products"}
          buttonText="Create Product"
          disabled={shouldDisableTaskCards}
        />
        <TaskCard
          title="Pending Orders (5)"
          description={isVerified ? "Please complete orders before they’re cancelled to avoid penalty" : "Complete verification to access orders"}
          buttonText="Check Orders"
          disabled={shouldDisableTaskCards}
        />
        <TaskCard
          title="New Promotions (5)"
          description={isVerified ? "You can take advantage of the promotions to sell more products" : "Complete verification to access promotions"}
          buttonText="Check Promotions"
          disabled={shouldDisableTaskCards}
        />
      </section>

      {/* Business Metrics */}
      <section className="mb-6">

        <Tabs defaultValue="7d" >
          <TabsList className="flex mx-0 items-center w-full justify-between">
             <h2 className="text-sm uppercase text-gray-600 font-semibold ">Business Metrics</h2>
             <section className="gap-1 flex">
              <TabsTrigger className="rounded-xl bg-primary/80 hover:bg-primary/90 text-primary-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm text-sm transition-colors duration-200" value="7d">7 Days</TabsTrigger>
              <TabsTrigger className="rounded-xl bg-primary/80 hover:bg-primary/90 text-primary-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm text-sm transition-colors duration-200" value="30d">30 Days</TabsTrigger>
              <TabsTrigger className="rounded-xl bg-primary/80 hover:bg-primary/90 text-primary-foreground data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-sm text-sm transition-colors duration-200" value="90d">90 Days</TabsTrigger>
             </section>

          </TabsList>

          <TabsContent value='7d'>
            <div className="grid grid-cols-2  gap-2 mt-3">
              <MetricCard
                title="Revenue"
                value={metrics[activeTab].revenue}
                trend={metrics[activeTab].trend}
                percentage={metrics[activeTab].percentage}
              />
              <MetricCard title="Items Sold" value="125" trend="up" percentage="5%" />
            </div>
          </TabsContent>
          <TabsContent value="30d">
            <div className="grid grid-cols-2  gap-2 mt-3">
              <MetricCard
                title="Revenue"
                value={metrics['30d'].revenue}
                trend={metrics['30d'].trend}
                percentage={metrics['30d'].percentage}
              />
              <MetricCard title="Items Sold" value="125" trend="up" percentage="5%" />
            </div>
          </TabsContent>
          <TabsContent value="90d">
            <div className="grid grid-cols-2  gap-2 mt-3">
              <MetricCard
                title="Revenue"
                value={metrics['90d'].revenue}
                trend={metrics['90d'].trend}
                percentage={metrics['90d'].percentage}
              />
              <MetricCard title="Items Sold" value="125" trend="up" percentage="5%" />
            </div>
          </TabsContent>
        </Tabs>
      </section>

      {/* Seller Score */}
      <section>
        <h2 className="text-sm uppercase text-gray-600 font-semibold mb-6">Seller Score</h2>
        <div className="flex justify-center relative">
          <div className="relative w-40 h-40 flex items-center justify-center">
            {/* Outer Circle - Customer Rating */}
            <svg width="160" height="160" className="absolute">
              <circle
                cx="80"
                cy="80"
                r="75"
                strokeWidth="2"
                fill="transparent"
                className="stroke-gray-200"
              />
              <circle
                 cx="80"
                 cy="80"
                 r="75"
                 strokeWidth="2"
                fill="transparent"
                stroke="hsl(141, 53%, 53%)" // Soft green
                strokeDasharray={2 * Math.PI * 75}
                strokeDashoffset={2 * Math.PI * 75 - (75 / 100) * 2 * Math.PI * 75}
                strokeLinecap="round"
              />
            </svg>

            {/* Middle Circle - Quality Return Rate */}
             <svg width="85%" height="85%" className="absolute">
                <circle
                  cx="50%"
                  cy="50%"
                  r="65"
                  strokeWidth="2"
                  fill="transparent"
                  className="stroke-gray-200"
                />
                <circle
                  cx="50%"
                  cy="50%"
                  r="65"
                  strokeWidth="2"
                  fill="transparent"
                  stroke="#8ec5ff" // Soft blue
                  strokeDasharray={2 * Math.PI * 65}
                  strokeDashoffset={2 * Math.PI * 65 - (85 / 100) * 2 * Math.PI * 65}
                  strokeLinecap="round"
                  className="transition-all duration-500"
                />
              </svg>


            {/* Inner Circle - Shipping Speed */}
            <svg width="70%" height="70%" className="absolute">
                <circle
                  cx="50%"
                  cy="50%"
                  r="55"
                  strokeWidth="2"
                  fill="transparent"
                  className="stroke-gray-200"
                />
                <circle
                  cx="50%"
                  cy="50%"
                  r="55"
                  strokeWidth="2"
                  fill="transparent"
                  stroke="hsl(var(--primary))" // Primary color
                  strokeDasharray={2 * Math.PI * 55}
                  strokeDashoffset={2 * Math.PI * 55 - (88 / 100) * 2 * Math.PI * 55}
                  strokeLinecap="round"
                  className="transition-all duration-500"
                />
              </svg>


            {/* Center Value */}
            <div className="absolute flex flex-col items-center">
              <span className=" font-bold">96%</span>
              <span className="text-sm text-gray-600">Rating</span>
            </div>
          </div>
        </div>

        <div className="mt-6 text-center">
          <div className="flex flex-col items-center space-y-4">
            <Legend color="hsl(141, 53%, 53%)" label="Shipping Speed" value="92%" />
            <Legend color="#8ec5ff" label="Quality Return Rate" value="0%" />
            <Legend color="hsl(217, 91%, 60%)" label="Average Customer Rate" value="96%" />
          </div>
        </div>
      </section>
    </div>
  );
}

// Reusable Task Card Component
function TaskCard({ title, description, buttonText, disabled = false }: { title: string; description: string; buttonText: string; disabled?: boolean }) {
  return (
    <div className={`bg-white p-4 rounded-lg shadow-lg ${disabled ? 'opacity-60' : ''}`}>
      <h3 className="text-sm text-gray-700 font-bold mb-2">{title}</h3>
      <p className="text-sm text-gray-600 mb-6">{description}</p>
      <div className={`w-full px-2 py-2 rounded-md flex justify-center items-center ${disabled ? 'bg-gray-100' : 'bg-primary-50'}`}>
        {disabled ? (
          <span className="text-sm uppercase text-gray-500 font-medium w-full flex justify-center cursor-not-allowed">
            {buttonText}
          </span>
        ) : (
          <Link href="#" className="text-sm uppercase text-primary bg-primary-50 font-medium w-full flex justify-center hover:text-primary/80 transition-colors duration-200">
            {buttonText}
          </Link>
        )}
      </div>
    </div>
  );
}

// Reusable Metric Card Component
function MetricCard({ title, value, trend, percentage }: { title: string; value: string; trend: string; percentage: string }) {
  const isUp = trend === "up";
  return (
    <div className="bg-white p-2 flex flex-col w-full items-center justify-center rounded-lg shadow-lg">
      <h4 className="text-gray-600 text-sm mb-2">{title}</h4>
      <h1 className="mb-3 text-gray-800 font-semibold">
        {value}
      </h1>
      <p className={`text-sm ${isUp ? "text-green-500" : "text-red-500"} flex items-center`}>
        {isUp ? <FaArrowUp /> : <FaArrowDown />} {percentage}
      </p>
    </div>
  );
}


const getRemark = (label: string, value: string) => {
  const numValue = parseFloat(value.replace('%', ''));

  switch (label) {
    case 'Average Customer Rate':
      if (numValue >= 90) return { text: 'Excellent', color: 'text-green-700 bg-green-100' };
      if (numValue >= 80) return { text: 'Good', color: 'text-primary-700 bg-primary-100' };
      if (numValue >= 70) return { text: 'Average', color: 'text-yellow-700 bg-yellow-100' };
      return { text: 'Poor', color: 'text-red-700 bg-red-100' };

    case 'Quality Return Rate':
      if (numValue <= 5) return { text: 'Excellent', color: 'text-green-700 bg-green-100' };
      if (numValue <= 10) return { text: 'Good', color: 'text-primary-700 bg-primary-100' };
      if (numValue <= 20) return { text: 'Average', color: 'text-yellow-700 bg-yellow-100' };
      return { text: 'Poor', color: 'text-red-700 bg-red-100' };

    case 'Shipping Speed':
      if (numValue >= 95) return { text: 'Excellent', color: 'text-green-700 bg-green-100' };
      if (numValue >= 85) return { text: 'Good', color: 'text-primary-700 bg-primary-100' };
      if (numValue >= 70) return { text: 'Average', color: 'text-yellow-700 bg-yellow-100' };
      return { text: 'Poor', color: 'text-red-700 bg-red-100' };

    default:
      return { text: 'N/A', color: 'text-gray-700 bg-gray-100' };
  }
}


// Legend Component
function Legend({ color, label, value }: { color: string; label: string; value: string }) {
  const { text: remark, color: remarkColor } = getRemark(label, value);

  return (
    <div className="flex w-full items-center justify-between px-2">
      <div className="flex items-center space-x-2">
        <div style={{ backgroundColor: color }} className="w-4 h-4 rounded-full" />
        <span className="text-sm font-medium">{label}</span>
      </div>
      <span className={`text-xs font-semibold px-2 py-1 rounded ${remarkColor}`}>
        {remark}
      </span>
      <span className="text-sm font-medium">{value}</span>
    </div>
  );
}


