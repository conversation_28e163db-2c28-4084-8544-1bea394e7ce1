'use client';

import React, { Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import FullScreenLoader from '@/components/ui/FullScreenLoader';

import OnboardingLayout from './_components/OnboardingLayout';
import { BusinessInfoForm, PaymentInfoForm, ShopInfoForm, ShippingInfoForm } from '@/lib/components/shared/forms';
import { useOnboardingStatusFromContext } from '@/lib/providers/status-provider';



const OnboardingPageContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isLoading, nextStep, needsOnboarding } = useOnboardingStatusFromContext();
  const currentStep = searchParams.get("step") || "business-info";

  // Redirect to next step if no step specified and onboarding needed
  React.useEffect(() => {
    if (!isLoading && nextStep && !searchParams.get("step")) {
      router.push(`/creators/onboarding?step=${nextStep}`);
    } else if (!isLoading && !needsOnboarding) {
      // If onboarding is complete, redirect to creator dashboard
      router.push('/creators');
    }
  }, [isLoading, nextStep, needsOnboarding, router, searchParams]);

  const handleStepComplete = () => {
    // Navigate to next step based on current step
    switch (currentStep) {
      case 'business-info':
        router.push('/creators/onboarding?step=payment-info');
        break;
      case 'payment-info':
        router.push('/creators/onboarding?step=shop-info');
        break;
      case 'shop-info':
        router.push('/creators/onboarding?step=shipping-info');
        break;
      case 'shipping-info':
        // Onboarding complete, redirect to dashboard
        router.push('/creators');
        break;
      default:
        break;
    }
  };

  if (isLoading) {
    return <FullScreenLoader text="Loading onboarding status..." />;
  }

  if (!needsOnboarding) {
    return <FullScreenLoader text="Redirecting to dashboard..." />;
  }

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'business-info':
        return <BusinessInfoForm onNext={handleStepComplete} />;
      case 'payment-info':
        return <PaymentInfoForm onSuccess={handleStepComplete} onBack={() => router.push('/creators/onboarding?step=business-info')} />;
      case 'shop-info':
        return <ShopInfoForm onSuccess={handleStepComplete} onBack={() => router.push('/creators/onboarding?step=payment-info')} />;
      case 'shipping-info':
        return <ShippingInfoForm onSuccess={handleStepComplete} onBack={() => router.push('/creators/onboarding?step=shop-info')} />;
      default:
        return <BusinessInfoForm onNext={handleStepComplete} />;
    }
  };

  return (
    <OnboardingLayout>
      {renderCurrentStep()}
    </OnboardingLayout>
  );
};

const OnboardingPage = () => {
  return (
    <Suspense fallback={<FullScreenLoader text="Loading..." />}>
      <OnboardingPageContent />
    </Suspense>
  );
};

export default OnboardingPage;
