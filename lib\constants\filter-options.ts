// Static filter options for the catalog page

export interface SortOption {
  value: string;
  label: string;
}

export interface FilterOptions {
  brands: string[];
  sizes: string[];
  genders: string[];
  countries: string[];
  conditions: string[];
  sortOptions: SortOption[];
  priceRange: {
    min: number;
    max: number;
  };
}

// Comprehensive list of fashion brands
export const FASHION_BRANDS = [
  // Luxury Brands
  "Gucci", "Louis Vuitton", "Chanel", "Prada", "Hermès", "Dior", "Versace", "Armani", "Dolce & Gabbana", "Burberry",
  "Saint Laurent", "Balenciaga", "Givenchy", "Valentino", "Tom Ford", "Bottega Veneta", "Fendi", "Ce<PERSON>", "<PERSON>ewe",
  
  // High-End Brands
  "Ralph Lauren", "Calvin Klein", "Tommy Hilfiger", "Hugo Boss", "Lacoste", "Polo Ralph Lauren", "Michael Kors",
  "Kate Spade", "<PERSON>", "<PERSON> Jacobs", "<PERSON>", "<PERSON> B<PERSON>ch", "<PERSON>", "<PERSON>",
  
  // Popular Fashion Brands
  "Nike", "Adidas", "Puma", "Under Armour", "Reebok", "New Balance", "Converse", "Vans", "ASICS", "Fila",
  "Champion", "Jordan", "Supreme", "Off-White", "Stone Island", "Moncler", "Canada Goose", "The North Face",
  
  // Fast Fashion & Accessible Brands
  "Zara", "H&M", "Uniqlo", "Forever 21", "Topshop", "ASOS", "Boohoo", "<PERSON>in", "Primark", "Mango",
  "COS", "& Other Stories", "Monki", "Weekday", "Bershka", "Pull & Bear", "Stradivarius",
  
  // Denim Brands
  "Levi's", "Wrangler", "Lee", "Diesel", "G-Star RAW", "True Religion", "7 For All Mankind", "Citizens of Humanity",
  "AG Jeans", "Paige", "Frame", "Acne Studios", "Nudie Jeans", "APC",
  
  // Streetwear & Urban
  "Stussy", "Kith", "Fear of God", "Essentials", "Palm Angels", "Rhude", "Amiri", "Gallery Dept", "Human Made",
  "A Bathing Ape", "Comme des Garçons", "Mastermind Japan", "Neighborhood", "Visvim",
  
  // Women's Fashion
  "Victoria's Secret", "Anthropologie", "Free People", "Urban Outfitters", "Reformation", "Ganni", "Sandro",
  "Maje", "Isabel Marant", "Zimmermann", "Self-Portrait", "Grlfrnd", "Revolve", "Realisation Par",
  
  // Men's Fashion
  "Brunello Cucinelli", "Ermenegildo Zegna", "Canali", "Brioni", "Kiton", "Isaia", "Loro Piana", "Boglioli",
  "Officine Generale", "AMI Paris", "Jacquemus", "Lemaire", "Maison Margiela",
  
  // Children's Brands
  "Carter's", "OshKosh B'gosh", "Gap Kids", "H&M Kids", "Zara Kids", "Nike Kids", "Adidas Kids", "Converse Kids",
  "Polo Ralph Lauren Children", "Tommy Hilfiger Kids", "Guess Kids", "Burberry Children",
  
  // Athletic & Activewear
  "Lululemon", "Athleta", "Alo Yoga", "Outdoor Voices", "Patagonia", "Arc'teryx", "Salomon", "Mammut",
  "Columbia", "REI Co-op", "Smartwool", "Merrell", "Timberland", "Dr. Martens",
  
  // Affordable & Value Brands
  "Target", "Walmart", "Old Navy", "Gap", "Banana Republic", "J.Crew", "American Eagle", "Hollister",
  "Abercrombie & Fitch", "Express", "Ann Taylor", "Loft", "Chico's", "Talbots"
];

// Comprehensive clothing and shoe sizes
export const CLOTHING_SIZES = [
  // Women's Clothing Sizes
  "XXS", "XS", "S", "M", "L", "XL", "XXL", "XXXL", "4XL", "5XL",

  // Numeric Women's Sizes
  "0", "2", "4", "6", "8", "10", "12", "14", "16", "18", "20", "22", "24", "26", "28", "30",

  // Men's Shirt Sizes
  "14", "14.5", "15", "15.5", "16", "16.5", "17", "17.5", "18", "18.5", "19", "19.5", "20",

  // Waist Sizes (Pants)
  "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40",
  "41", "42", "43", "44", "45", "46", "48", "50", "52", "54", "56",

  // Shoe Sizes - Women's US
  "5", "5.5", "6", "6.5", "7", "7.5", "8", "8.5", "9", "9.5", "10", "10.5", "11", "11.5", "12",

  // Shoe Sizes - Men's US
  "12.5", "13", "13.5", "14", "15", "16",

  // Children's Sizes
  "2T", "3T", "4T", "5T", "6X", "7", "8",

  // Kid's Numeric Sizes
  "5", "6", "10", "12", "14", "16",

  // Baby Sizes
  "Newborn", "0-3M", "3-6M", "6-9M", "9-12M", "12-18M", "18-24M",

  // International Shoe Sizes (EU)
  "34", "35", "36", "37", "38", "39", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50"
];


// Gender options
export const GENDERS = ["Male", "Female", "Unisex"];

// Condition options
export const CONDITIONS = ["Excellent", "Good", "Poor"];

// Sort options
export const SORT_OPTIONS: SortOption[] = [
  { value: "newest", label: "Newest First" },
  { value: "oldest", label: "Oldest First" },
  { value: "price-low-high", label: "Price: Low to High" },
  { value: "price-high-low", label: "Price: High to Low" },
  { value: "rating", label: "Highest Rated" },
  { value: "popular", label: "Most Popular" }
];

// Price range
export const PRICE_RANGE = {
  min: 0,
  max: 10000
};

// Countries list (major fashion markets and common shipping destinations)
export const COUNTRIES = [
  "United States", "United Kingdom", "Canada", "Australia", "Germany", "France", "Italy", "Spain",
  "Netherlands", "Belgium", "Switzerland", "Austria", "Sweden", "Norway", "Denmark", "Finland",
  "Japan", "South Korea", "Singapore", "Hong Kong", "China", "India", "Brazil", "Mexico",
  "Argentina", "Chile", "South Africa", "Nigeria", "Kenya", "Ghana", "Egypt", "Morocco",
  "Turkey", "Greece", "Portugal", "Ireland", "Poland", "Czech Republic", "Hungary", "Romania",
  "Bulgaria", "Croatia", "Slovenia", "Slovakia", "Estonia", "Latvia", "Lithuania", "Russia",
  "Ukraine", "Belarus", "Kazakhstan", "Uzbekistan", "Thailand", "Vietnam", "Malaysia", "Indonesia",
  "Philippines", "Taiwan", "New Zealand", "Israel", "UAE", "Saudi Arabia", "Qatar", "Kuwait",
  "Bahrain", "Oman", "Jordan", "Lebanon", "Cyprus", "Malta", "Iceland", "Luxembourg", "Monaco"
];

// Combined filter options
export const FILTER_OPTIONS: FilterOptions = {
  brands: FASHION_BRANDS,
  sizes: CLOTHING_SIZES,
  genders: GENDERS,
  countries: COUNTRIES,
  conditions: CONDITIONS,
  sortOptions: SORT_OPTIONS,
  priceRange: PRICE_RANGE
};
