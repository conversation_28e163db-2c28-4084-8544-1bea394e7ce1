'use client';

import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { Shield, AlertTriangle, Clock, XCircle, FileText, ArrowRight, Store } from 'lucide-react';
import { motion } from 'framer-motion';
import <PERSON><PERSON><PERSON>oader from '@/components/ui/LottieLoader';

interface VerificationPromptProps {
  type: 'products' | 'orders' | 'earnings' | 'promotions' | 'bales' | 'feedback' | 'general';
  verificationStatus?: 'pending' | 'verified' | 'rejected' | 'not_submitted' | 'unverified';
  title?: string;
  description?: string;
}

const VerificationPrompt = ({
  type,
  verificationStatus = 'unverified',
  title,
  description
}: VerificationPromptProps) => {
  const router = useRouter();

  const getPageConfig = () => {
    const baseConfig = {
      products: {
        icon: Store,
        title: title || "Manage Your Products",
        description: description || "Upload products, manage inventory, and track performance. Verification required to start selling on Everyfash.",
      },
      orders: {
        icon: FileText,
        title: title || "Track Your Orders",
        description: description || "View customer orders, manage fulfillment, and track sales. Complete verification to access your order dashboard.",
      },
      earnings: {
        icon: ArrowRight,
        title: title || "View Your Earnings",
        description: description || "Monitor your revenue, track payouts, and analyze performance. Verification needed to access financial data.",
      },
      promotions: {
        icon: ArrowRight,
        title: title || "Create Promotions",
        description: description || "Set up discounts, run campaigns, and boost sales. Complete verification to access promotional tools.",
      },
      bales: {
        icon: ArrowRight,
        title: title || "Manage Bales",
        description: description || "Organize bulk inventory and manage wholesale operations. Verification required for bale management.",
      },
      feedback: {
        icon: ArrowRight,
        title: title || "Customer Feedback",
        description: description || "View reviews, respond to customers, and improve your service. Complete verification to access feedback tools.",
      },
      general: {
        icon: Shield,
        title: title || "Complete Account Verification",
        description: description || "Verify your account to access all creator features and start building your business on Everyfash.",
      },
    };

    return baseConfig[type] || baseConfig.general;
  };

  const getVerificationConfig = () => {
    switch (verificationStatus) {
      case 'verified':
        return {
          statusIcon: Shield,
          statusColor: 'text-green-600',
          statusBg: 'bg-green-50',
          statusText: 'Account Verified',
          message: 'Your account is verified! You should have access to this feature.',
          actionText: 'Contact Support',
          actionVariant: 'outline' as const,
        };
      case 'pending':
        return {
          statusIcon: Clock,
          statusColor: 'text-amber-600',
          statusBg: 'bg-amber-50',
          statusText: 'Verification Pending',
          message: 'Your verification is under review. You\'ll gain access once active.',
          actionText: 'Check Status',
          actionVariant: 'outline' as const,
        };
      case 'rejected':
        return {
          statusIcon: XCircle,
          statusColor: 'text-red-600',
          statusBg: 'bg-red-50',
          statusText: 'Verification Rejected',
          message: 'Your verification was rejected. Please resubmit your documents.',
          actionText: 'Resubmit Documents',
          actionVariant: 'default' as const,
        };
      case 'unverified':
        return {
          statusIcon: AlertTriangle,
          statusColor: 'text-orange-600',
          statusBg: 'bg-orange-50',
          statusText: 'Account Unverified',
          message: 'Your account is not verified. Complete verification to access this feature.',
          actionText: 'Complete Verification',
          actionVariant: 'default' as const,
        };
      case 'not_submitted':
      default:
        return {
          statusIcon: AlertTriangle,
          statusColor: 'text-orange-600',
          statusBg: 'bg-orange-50',
          statusText: 'Verification Required',
          message: 'Complete account verification to access this feature.',
          actionText: 'Start Verification',
          actionVariant: 'default' as const,
        };
    }
  };

  const config = getPageConfig();
  const verificationConfig = getVerificationConfig();
  const IconComponent = config.icon;
  const StatusIconComponent = verificationConfig.statusIcon;

  const handleAction = () => {
    if (verificationStatus === 'verified') {
      // Contact support or refresh page
      window.location.reload();
    } else {
      // Navigate to profile/verification page
      router.push('/creators/profile');
    }
  };

  const handleGoToProfile = () => {
    router.push('/creators/profile');
  };

  return (
    <section className="w-full mt-16 py-4 flex items-center justify-center">
      <div className="w-full mx-auto px-2">
        {/* Main Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center"
        >
          {/* Feature Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center"
          >
            <IconComponent className="w-8 h-8 text-gray-600" />
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-lg font-semibold text-gray-800 mb-3"
          >
            {config.title}
          </motion.h1>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-gray-600 mb-6 text-sm leading-relaxed"
          >
            {config.description}
          </motion.p>

          {/* Verification Status Card */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className={`${verificationConfig.statusBg} border border-gray-200 rounded-lg p-4 mb-6`}
          >
            <div className="flex items-center justify-center space-x-3">
              {/* <StatusIconComponent className={`w-5 h-5 ${verificationConfig.statusColor}`} /> */}
              <div>
                <h3 className={`text-sm font-semibold ${verificationConfig.statusColor}`}>
                  {verificationConfig.statusText}
                </h3>
                <p className="text-xs text-gray-600 mt-1">
                  {verificationConfig.message}
                </p>
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-3"
          >
            <Button
              onClick={handleAction}
              variant={verificationConfig.actionVariant}
              className="w-full h-10 transition-colors duration-200"
            >
              {verificationConfig.actionText}
            </Button>

            <Button
              onClick={handleGoToProfile}
              variant="outline"
              className="w-full h-10 border-gray-300 hover:bg-gray-50 transition-colors duration-200"
            >
              Go to Profile
            </Button>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
            className="mt-4 text-xs text-gray-500"
          >
            Verification helps protect buyers and sellers on Everyfash
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default VerificationPrompt;
