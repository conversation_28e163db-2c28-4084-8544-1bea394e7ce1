// Authentication types and interfaces

export type UserRole = 'buyer' | 'creator';

export interface User {
  id: string;
  _id: string;
  name: string;
  email: string;
  photo?: string;
  role: UserRole;
  userType: 'Buyer' | 'Creator';
  socialProvider: string | null;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;

  // Buyer-specific fields
  preferences?: {
    priceRange?: {
      min: number;
      max: number;
    };
    notificationPreferences: {
      orderUpdates: boolean;
      promotions: boolean;
      priceDrops: boolean;
      newArrivals: boolean;
      email: boolean;
      push: boolean;
    };
    categories: string[];
    sizes: string[];
    colors: string[];
  };
  followedCreators?: string[];
  loyaltyPoints?: {
    balance: number;
    tier: string;
    history: any[];
  };
  addresses?: any[];
  recentlyViewed?: any[];
  baleWishlist?: any[];
  paymentMethods?: any[];

  // Creator-specific fields
  businessInfo?: {
    businessAddress: {
      country: string;
    };
    isVerified: boolean;
    verificationDocuments: any[];
  };
  metrics?: {
    totalSales: number;
    totalRevenue: number;
    averageRating: number;
    qualityScore: number;
    shippingSpeed: number;
    totalProducts: number;
    totalBales: number;
    followers: number;
  };
  payoutPreferences?: {
    frequency: string;
    minimumAmount: number;
  };
  shopInfo?: {
    customerCare: {
      country: string;
    };
  };
  shippingInfo?: {
    shippingAddress: {
      country: string;
    };
    returnAddress: {
      useSameAddress: boolean;
      country: string;
    };
  };
  verificationStatus?: string;
  verificationDetails?: {
    documents: any[];
  };
  commissionRate?: number;
  sellerProfile?: {
    sellsOffline: boolean;
    usesOtherChannels: boolean;
  };
  onboardingStatus?: string;
  onboardingProgress?: {
    businessInfo: boolean;
    paymentInfo: boolean;
    shopInfo: boolean;
    shippingInfo: boolean;
  };

  // Common fields
  unreadNotificationsCount?: any;
  notificationPreferences?: any;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  role: UserRole;
}

export interface RegisterCredentials {
  name: string;
  email: string;
  password: string;
  passwordConfirm: string;
  role: UserRole;
}

export interface AuthResponse {
  status: string;
  token: string;
  data: {
    user: User;
  };
}

export interface ApiError {
  message: string;
  code?: string;
  field?: string;
  status?: number;
}

export interface ForgotPasswordCredentials {
  email: string;
}

export interface ForgotPasswordResponse {
  status: string;
  message: string;
}

export interface ResetPasswordCredentials {
  password: string;
  passwordConfirm: string;
}

export interface ResetPasswordResponse {
  status: string;
  token: string;
  data: {
    user: User;
  };
}

export interface EmailVerificationResponse {
  status: string;
  token: string;
  data: {
    user: User;
  };
}

export interface ResendVerificationCredentials {
  email: string;
}

export interface ResendVerificationResponse {
  status: string;
  message: string;
}

export interface RegistrationResponse {
  status: string;
  message: string;
  data: {
    user: User;
  };
}

export interface UnverifiedLoginResponse {
  status: string;
  message: string;
  emailSent: boolean;
  userEmail: string;
}

// Creator Onboarding Types
export interface BusinessAddress {
  country: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  digitalGps?: string;
  state: string;
}

// Payment information types
export interface BankDetails {
  accountNumber: string;
  accountName: string;
  bankName: string;
  branchName?: string;
  swiftCode?: string;
}

export interface MobileMoneyDetails {
  serviceProvider: string;
  registeredNumber: string;
  registeredName: string;
}

export interface PayoutPreferences {
  frequency: 'weekly' | 'monthly' | 'bi-weekly';
  minimumAmount: number;
  automaticPayouts?: boolean;
}

export interface PaymentInfo {
  bankDetails?: BankDetails;
  mobileMoneyDetails?: MobileMoneyDetails;
  paymentOption?: 'bank' | 'mobile_money';
}

export interface PaymentInfoData {
  paymentInfo: PaymentInfo;
  payoutPreferences: PayoutPreferences;
  onboardingStatus?: string;
  onboardingProgress?: OnboardingProgress;
}

export interface PaymentInfoResponse {
  status: string;
  data: PaymentInfoData;
  message?: string;
}

export interface PaymentInfoUpdateData {
  paymentOption: 'bank' | 'mobile_money';
  bankDetails?: BankDetails;
  mobileMoneyDetails?: MobileMoneyDetails;
  payoutPreferences: PayoutPreferences;
}

export interface BusinessInfo {
  businessAddress: BusinessAddress;
  isVerified: boolean;
  verificationDocuments: string[];
  businessName: string;
  businessType: string;
  ownerID: string;
  ownerName: string;
  phoneNumber: string;
  taxId: string;
}

export interface OnboardingProgress {
  businessInfo: boolean;
  paymentInfo: boolean;
  shopInfo: boolean;
  shippingInfo: boolean;
}

export interface SocialMedia {
  instagram?: string;
  facebook?: string;
  twitter?: string;
  tiktok?: string;
  youtube?: string;
  website?: string;
}

export interface VerificationDetails {
  submittedAt: string;
  documents: string[];
}

export interface BusinessInfoResponse {
  status: string;
  data: {
    businessInfo: BusinessInfo;
    verificationStatus: string;
    verificationDetails?: VerificationDetails;
    onboardingStatus?: string;
    onboardingProgress?: OnboardingProgress;
  };
}

export interface BusinessInfoUpdateData {
  businessName: string;
  businessType: string;
  ownerName: string;
  ownerID: string;
  phoneNumber: string;
  taxId: string;
  businessAddress: BusinessAddress;
  verificationDocuments?: (File | string)[]; // Can be File objects (new) or URLs (existing to keep)
}

export interface ShopInfoData {
  shopInfo: {
    name?: string;
    description?: string;
    logo?: string;
    banner?: string;
    sellerType?: 'individual' | 'business' | 'both';
    salesChannels?: string[]; // Added missing field
    contact?: {
      name?: string;
      email?: string;
      phone?: string;
    };
    customerCare?: {
      name?: string;
      email?: string;
      phone?: string;
      addressLine1?: string;
      addressLine2?: string;
      city?: string;
      region?: string;
      country?: string;
      hours?: string;
      supportWebsite?: string;
    };
    policies?: {
      returns?: string;
      shipping?: string;
      privacy?: string;
      additionalPolicies?: string;
    };
  };
  socialMedia?: SocialMedia;
  sellerProfile?: {
    bio?: string;
    specialties?: string[];
    experience?: string;
    sellsOffline?: boolean;
    sellerType?: 'individual' | 'business' | 'both'; // Added missing field
    usesOtherChannels?: boolean;
  };
  onboardingStatus?: string;
  onboardingProgress?: OnboardingProgress;
}

export interface ShopInfoResponse {
  status: string;
  data: ShopInfoData;
  message?: string;
}

export interface ShopInfoUpdateData {
  // Required fields (per API docs)
  name: string;
  sellerType: 'individual' | 'business' | 'both';
  contact: {
    name: string;
    email: string;
    phone: string;
  };
  // Optional fields
  description?: string;
  logo?: File;
  banner?: File;
  socialMedia?: SocialMedia;
  customerCare?: {
    name?: string;
    email?: string;
    phone?: string;
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    region?: string;
    country?: string;
    hours?: string;
    supportWebsite?: string;
  };
}

export interface OnboardingStatusResponse {
  status: string;
  data: {
    onboardingStatus: string;
    onboardingProgress: OnboardingProgress;
    isComplete: boolean;
  };
}

export interface ChangePasswordCredentials {
  currentPassword: string;
  newPassword: string;
}

// Shipping information types
export interface ShippingAddress {
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  zone: string;
  country: string;
  postalCode?: string;
  digitalGps?: string;
  phone?: string;
}

export interface ReturnAddress {
  useSameAddress: boolean;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zone?: string;
  country?: string;
  postalCode?: string;
  digitalGps?: string;
  phone?: string;
}

export interface ShippingMethod {
  name: string;
  price: number;
  estimatedDelivery: string;
}

export interface ShippingInfo {
  shippingAddress: ShippingAddress;
  returnAddress: ReturnAddress;
  policy?: string;
  methods?: ShippingMethod[];
}

export interface ShippingInfoData {
  shippingInfo: ShippingInfo;
  onboardingStatus?: string;
  onboardingProgress?: OnboardingProgress;
  isComplete?: boolean;
}

export interface ShippingInfoResponse {
  status: string;
  data: ShippingInfoData;
  message?: string;
}

export interface ShippingInfoUpdateData {
  shippingAddress: ShippingAddress;
  returnAddress: ReturnAddress;
  policy?: string;
  methods?: ShippingMethod[];
}

// Verification status types
export interface VerificationDetails {
  submittedAt: string;
  documents: string[];
}

export interface CreatorInfo {
  name: string;
  email: string;
}

export interface VerificationStatusData {
  verificationStatus: 'pending' | 'verified' | 'unverified' | 'rejected' | 'not_submitted';
  verificationDetails?: VerificationDetails;
  documentsSubmitted: string[];
  onboardingStatus: string;
  onboardingProgress: OnboardingProgress;
  creatorInfo: CreatorInfo;
  statusMessage: string;
  nextSteps: string[];
}

export interface VerificationStatusResponse {
  status: string;
  data: VerificationStatusData;
  message?: string;
}
