/**
 * Request Deduplication Utility
 * Prevents multiple identical API calls from being made simultaneously
 */

interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
}

class RequestDeduplicator {
  private pendingRequests = new Map<string, PendingRequest>();
  private readonly CACHE_DURATION = 5000; // 5 seconds

  /**
   * Generate a unique key for the request
   */
  private generateKey(url: string, options?: RequestInit): string {
    const method = options?.method || 'GET';
    const body = options?.body ? JSON.stringify(options.body) : '';
    const headers = options?.headers ? JSON.stringify(options.headers) : '';
    return `${method}:${url}:${body}:${headers}`;
  }

  /**
   * Clean up expired requests
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, request] of this.pendingRequests.entries()) {
      if (now - request.timestamp > this.CACHE_DURATION) {
        this.pendingRequests.delete(key);
      }
    }
  }

  /**
   * Deduplicate a request
   */
  async deduplicate<T>(
    url: string,
    requestFn: () => Promise<T>,
    options?: RequestInit
  ): Promise<T> {
    this.cleanup();
    
    const key = this.generateKey(url, options);
    const existing = this.pendingRequests.get(key);

    if (existing) {
      // Return the existing promise
      return existing.promise;
    }

    // Create new request
    const promise = requestFn().finally(() => {
      // Remove from pending requests when completed
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now(),
    });

    return promise;
  }

  /**
   * Clear all pending requests
   */
  clear(): void {
    this.pendingRequests.clear();
  }

  /**
   * Get the number of pending requests
   */
  getPendingCount(): number {
    this.cleanup();
    return this.pendingRequests.size;
  }
}

// Global instance
export const requestDeduplicator = new RequestDeduplicator();

/**
 * Higher-order function to wrap API functions with deduplication
 */
export function withDeduplication<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator?: (...args: Parameters<T>) => string
): T {
  return ((...args: Parameters<T>) => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    return requestDeduplicator.deduplicate(
      key,
      () => fn(...args)
    );
  }) as T;
}

/**
 * Decorator for class methods
 */
export function Deduplicate(keyGenerator?: (...args: any[]) => string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const key = keyGenerator ? keyGenerator(...args) : `${target.constructor.name}.${propertyKey}:${JSON.stringify(args)}`;
      return requestDeduplicator.deduplicate(
        key,
        () => originalMethod.apply(this, args)
      );
    };

    return descriptor;
  };
}

export default requestDeduplicator;
