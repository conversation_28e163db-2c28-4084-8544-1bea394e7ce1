"use client";

import React, { memo } from "react";
import Image from "next/image";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AiOutlineDelete } from "react-icons/ai";
import { Package } from "lucide-react";
import { ProductOrBale } from "@/lib/types/products";

interface ProductsTableProps {
  products: ProductOrBale[];
  isLoading: boolean;
  onViewDetails: (id: string) => void;
  onDeleteProduct: (id: string) => void;
}

const ProductsTable: React.FC<ProductsTableProps> = memo(({ 
  products, 
  isLoading, 
  onViewDetails, 
  onDeleteProduct 
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  if (isLoading) {
    return (
      <div className="w-full py-8 flex items-center justify-center">
        <Package className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading products...</span>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-8">
        <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <p className="text-gray-500">No products found</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Item</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Brand/Country</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>Stock</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-center">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((item: ProductOrBale) => (
            <TableRow key={item._id} className="whitespace-nowrap">
              {/* Item Column with Image & Name */}
              <TableCell className="flex items-center space-x-2 max-w-[250px]" onClick={() => onViewDetails(item._id)}>
                {item.images && item.images.length > 0 ? (
                  <div className="w-10 h-10 rounded overflow-hidden relative">
                    <Image
                      src={item.images[0]}
                      alt={item.name}
                      fill
                      sizes="40px"
                      className="object-cover"
                      onError={() => {
                        console.error('Item image failed to load:', item.images[0]);
                      }}
                    />
                  </div>
                ) : (
                  <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                    <Package className="h-5 w-5 text-gray-400" />
                  </div>
                )}
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate cursor-pointer hover:text-blue-600">
                    {item.name}
                  </p>
                </div>
              </TableCell>

              {/* Type */}
              <TableCell>
                <Badge variant="outline" className="capitalize">
                  {item.type}
                </Badge>
              </TableCell>

              {/* Brand/Country */}
              <TableCell>
                <span className="text-sm text-gray-900">
                  {item.type === 'product' ? (item as any).brand : (item as any).country}
                </span>
              </TableCell>

              {/* Price */}
              <TableCell>
                <span className="text-sm font-medium text-gray-900">
                  {formatPrice(item.basePrice)}
                </span>
              </TableCell>

              {/* Stock */}
              <TableCell>
                <span className={`text-sm font-medium ${
                  item.totalStock === 0 ? 'text-red-600' : 
                  item.totalStock <= 10 ? 'text-yellow-600' : 'text-green-600'
                }`}>
                  {item.totalStock}
                </span>
              </TableCell>

              {/* Status */}
              <TableCell>
                <Badge className={getStatusColor(item.status)}>
                  {item.status}
                </Badge>
              </TableCell>

              {/* Actions */}
              <TableCell className="text-center">
                <div className="flex items-center justify-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewDetails(item._id)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    View
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDeleteProduct(item._id)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <AiOutlineDelete className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
});

ProductsTable.displayName = 'ProductsTable';

export default ProductsTable;
