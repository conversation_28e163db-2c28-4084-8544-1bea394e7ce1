'use client';

import React from 'react';
import { useBuyerProducts } from '@/lib/hooks/use-buyer-products';
import { Button } from '@/components/ui/button';

const TestApiPage = () => {
  const { data, isLoading, error, isError } = useBuyerProducts({
    page: 1,
    limit: 5,
    sort: 'newest'
  });

  const handleTestCall = async () => {
    try {
      const response = await fetch('https://everyfash-api.onrender.com/api/v1/products?page=1&limit=2&sort=newest&usePreferences=false');
      const result = await response.json();
      console.log('Direct API call result:', result);
    } catch (error) {
      console.error('Direct API call error:', error);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Buyer Products API Test</h1>
      
      <div className="space-y-4">
        <Button onClick={handleTestCall}>
          Test Direct API Call
        </Button>
        
        <div className="border p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">React Query Hook Test</h2>
          
          {isLoading && (
            <div className="text-blue-600">Loading products...</div>
          )}
          
          {isError && (
            <div className="text-red-600">
              Error: {error?.message || 'Unknown error'}
            </div>
          )}
          
          {data && (
            <div className="space-y-2">
              <div className="text-green-600">✅ Success!</div>
              <div>Status: {data.status}</div>
              <div>Results: {data.results}</div>
              <div>Total: {data.total}</div>
              <div>Page: {data.page}</div>
              <div>Total Pages: {data.totalPages}</div>
              <div>Using Preferences: {data.usingPreferences ? 'Yes' : 'No'}</div>
              
              <div className="mt-4">
                <h3 className="font-semibold">Products:</h3>
                <div className="space-y-2">
                  {data.data.items.map((item, index) => (
                    <div key={item._id} className="border-l-2 border-gray-300 pl-2">
                      <div>#{index + 1} - {item.name}</div>
                      <div className="text-sm text-gray-600">
                        Type: {item.type} | Price: GH₵{item.basePrice} | 
                        Discount: {item.hasAnyDiscount ? `${item.maxDiscountPercentage}%` : 'None'}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TestApiPage;
