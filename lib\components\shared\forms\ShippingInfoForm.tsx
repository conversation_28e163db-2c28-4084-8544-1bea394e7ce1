'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useShippingInfo, useUpdateShippingInfo } from '@/lib/hooks/use-onboarding';
import { useShippingInfoProfile, useUpdateShippingInfoProfile } from '@/lib/hooks/useProfile';
import { ShippingInfoUpdateData } from '@/lib/types/auth';
import { toast } from '@/hooks/use-toast';
import { getApiErrorMessage, getApiErrorTitle } from '@/lib/utils/error-utils';
import {
  FormLayout,
  FormSection,
  FormInput,
  FormSelect,
  FormCheckbox,
  FormActions
} from './FormComponents';

// Ghana regions for select options
const GHANA_REGIONS = [
  'Greater Accra', 'Ashanti', 'Western', 'Central', 'Eastern', 'Northern',
  'Upper East', 'Upper West', 'Volta', 'Brong-Ahafo', 'Western North',
  'Ahafo', 'Bono', 'Bono East', 'Oti', 'Savannah', 'North East'
];

// Validation schema
const shippingInfoSchema = z.object({
  // Shipping address
  shippingAddressLine1: z.string().min(1, 'Address line 1 is required'),
  shippingAddressLine2: z.string().optional(),
  shippingCity: z.string().min(1, 'City is required'),
  shippingState: z.string().min(1, 'Region is required'),
  shippingZone: z.string().min(1, 'Zone is required'),
  shippingCountry: z.string().min(1, 'Country is required'),
  shippingPostalCode: z.string().optional(),
  shippingDigitalGps: z.string().optional(),
  shippingPhone: z.string().optional(),
  
  // Return address
  useSameAddress: z.boolean(),
  returnAddressLine1: z.string().optional(),
  returnAddressLine2: z.string().optional(),
  returnCity: z.string().optional(),
  returnState: z.string().optional(),
  returnZone: z.string().optional(),
  returnCountry: z.string().optional(),
  returnPostalCode: z.string().optional(),
  returnDigitalGps: z.string().optional(),
  returnPhone: z.string().optional(),
}).superRefine((data, ctx) => {
  // Validate return address fields when useSameAddress is false
  if (!data.useSameAddress) {
    if (!data.returnAddressLine1 || data.returnAddressLine1.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Return address line 1 is required when using different address',
        path: ['returnAddressLine1'],
      });
    }
    if (!data.returnCity || data.returnCity.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Return city is required when using different address',
        path: ['returnCity'],
      });
    }
    if (!data.returnState || data.returnState.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Return region is required when using different address',
        path: ['returnState'],
      });
    }
    if (!data.returnZone || data.returnZone.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Return zone is required when using different address',
        path: ['returnZone'],
      });
    }
    if (!data.returnCountry || data.returnCountry.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Return country is required when using different address',
        path: ['returnCountry'],
      });
    }
  }
});

type ShippingInfoFormData = z.infer<typeof shippingInfoSchema>;

interface ShippingInfoFormProps {
  onSuccess?: () => void;
  onBack?: () => void;
  isProfileMode?: boolean;
}

const ShippingInfoForm = ({ onSuccess, onBack, isProfileMode = false }: ShippingInfoFormProps) => {
  // Use different hooks based on mode
  const { data: shippingInfoResponse, isLoading: isLoadingShippingInfo } = isProfileMode
    ? useShippingInfoProfile()
    : useShippingInfo();
  const updateShippingInfoMutation = isProfileMode
    ? useUpdateShippingInfoProfile()
    : useUpdateShippingInfo();

  const shippingInfo = shippingInfoResponse?.data?.shippingInfo;

  const { control, handleSubmit, setValue, watch, formState: { errors }, trigger } = useForm<ShippingInfoFormData>({
    resolver: zodResolver(shippingInfoSchema),
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      shippingAddressLine1: '',
      shippingAddressLine2: '',
      shippingCity: '',
      shippingState: '',
      shippingZone: '',
      shippingCountry: 'Ghana',
      shippingPostalCode: '',
      shippingDigitalGps: '',
      shippingPhone: '',
      useSameAddress: true,
      returnAddressLine1: '',
      returnAddressLine2: '',
      returnCity: '',
      returnState: '',
      returnZone: '',
      returnCountry: 'Ghana',
      returnPostalCode: '',
      returnDigitalGps: '',
      returnPhone: '',
    },
  });

  const useSameAddress = watch('useSameAddress');

  // Update form when data loads
  useEffect(() => {
    if (shippingInfo) {
      
      // Shipping address
      setValue('shippingAddressLine1', shippingInfo.shippingAddress.addressLine1 || '');
      setValue('shippingAddressLine2', shippingInfo.shippingAddress.addressLine2 || '');
      setValue('shippingCity', shippingInfo.shippingAddress.city || '');
      setValue('shippingState', shippingInfo.shippingAddress.state || '');
      setValue('shippingZone', shippingInfo.shippingAddress.zone || '');
      setValue('shippingCountry', shippingInfo.shippingAddress.country || 'Ghana');
      setValue('shippingPostalCode', shippingInfo.shippingAddress.postalCode || '');
      setValue('shippingDigitalGps', shippingInfo.shippingAddress.digitalGps || '');
      setValue('shippingPhone', shippingInfo.shippingAddress.phone || '');
      
      // Return address
      setValue('useSameAddress', shippingInfo.returnAddress.useSameAddress);
      setValue('returnAddressLine1', shippingInfo.returnAddress.addressLine1 || '');
      setValue('returnAddressLine2', shippingInfo.returnAddress.addressLine2 || '');
      setValue('returnCity', shippingInfo.returnAddress.city || '');
      setValue('returnState', shippingInfo.returnAddress.state || '');
      setValue('returnZone', shippingInfo.returnAddress.zone || '');
      setValue('returnCountry', shippingInfo.returnAddress.country || 'Ghana');
      setValue('returnPostalCode', shippingInfo.returnAddress.postalCode || '');
      setValue('returnDigitalGps', shippingInfo.returnAddress.digitalGps || '');
      setValue('returnPhone', shippingInfo.returnAddress.phone || '');
    }
  }, [shippingInfo, setValue]);

  const onSubmit = async (data: ShippingInfoFormData) => {
    try {
      // Manually trigger validation to ensure all errors are caught
      const isValid = await trigger();

      if (!isValid) {
        // Get current errors after validation
        const currentErrors = Object.keys(errors);
        const errorMessages = currentErrors.map(key => {
          const error = errors[key as keyof ShippingInfoFormData];
          return error?.message;
        }).filter(Boolean);

        toast({
          variant: 'destructive',
          title: 'Please Fix Form Errors',
          description: errorMessages.length > 1
            ? `${errorMessages.length} fields need attention: ${errorMessages[0]} and ${errorMessages.length - 1} more.`
            : errorMessages[0] || 'Please fill in all required fields correctly.',
        });
        return;
      }

      const updateData: ShippingInfoUpdateData = {
        shippingAddress: {
          addressLine1: data.shippingAddressLine1,
          addressLine2: data.shippingAddressLine2,
          city: data.shippingCity,
          state: data.shippingState,
          zone: data.shippingZone,
          country: data.shippingCountry,
          postalCode: data.shippingPostalCode,
          digitalGps: data.shippingDigitalGps,
          phone: data.shippingPhone,
        },
        returnAddress: {
          useSameAddress: data.useSameAddress,
          addressLine1: data.useSameAddress ? data.shippingAddressLine1 : data.returnAddressLine1,
          addressLine2: data.useSameAddress ? data.shippingAddressLine2 : data.returnAddressLine2,
          city: data.useSameAddress ? data.shippingCity : data.returnCity,
          state: data.useSameAddress ? data.shippingState : data.returnState,
          zone: data.useSameAddress ? data.shippingZone : data.returnZone,
          country: data.useSameAddress ? data.shippingCountry : data.returnCountry,
          postalCode: data.useSameAddress ? data.shippingPostalCode : data.returnPostalCode,
          digitalGps: data.useSameAddress ? data.shippingDigitalGps : data.returnDigitalGps,
          phone: data.useSameAddress ? data.shippingPhone : data.returnPhone,
        },
      };

      await updateShippingInfoMutation.mutateAsync(updateData);

      // Show success message for completing onboarding
      toast({
        title: 'Onboarding Complete! 🎉',
        description: 'Welcome to Everyfash! Your creator account is now ready.',
        className: 'bg-green-100 text-green-800',
      });

      onSuccess?.();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: getApiErrorTitle(error),
        description: getApiErrorMessage(error),
      });
    }
  };

  if (isLoadingShippingInfo) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <FormLayout>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-4">
        {/* Shipping Address */}
        <FormSection
          title="Shipping Address"
          description="Where you'll ship orders from"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="Address Line 1"
              name="shippingAddressLine1"
              control={control}
              errors={errors}
              placeholder="Enter street address"
              required
            />

            <FormInput
              label="Address Line 2"
              name="shippingAddressLine2"
              control={control}
              errors={errors}
              placeholder="Apartment, suite, etc. (optional)"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormInput
              label="City"
              name="shippingCity"
              control={control}
              errors={errors}
              placeholder="Enter city"
              required
            />

            <FormSelect
              label="Region"
              name="shippingState"
              control={control}
              errors={errors}
              placeholder="Select region"
              options={GHANA_REGIONS.map(region => ({ value: region, label: region }))}
              required
            />

            <FormInput
              label="Zone"
              name="shippingZone"
              control={control}
              errors={errors}
              placeholder="Enter zone"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormSelect
              label="Country"
              name="shippingCountry"
              control={control}
              errors={errors}
              options={[{ value: 'Ghana', label: 'Ghana' }]}
              required
            />

            <FormInput
              label="Postal Code"
              name="shippingPostalCode"
              control={control}
              errors={errors}
              placeholder="Enter postal code (optional)"
            />

            <FormInput
              label="Digital GPS"
              name="shippingDigitalGps"
              control={control}
              errors={errors}
              placeholder="Enter GPS address (optional)"
            />
          </div>

          <FormInput
            label="Phone Number"
            name="shippingPhone"
            control={control}
            errors={errors}
            placeholder="Enter phone number (optional)"
          />
        </FormSection>

        {/* Return Address */}
        <FormSection
          title="Return Address"
          description="Where customers can return items"
        >
          <FormCheckbox
            label="Use same address as shipping address"
            name="useSameAddress"
            control={control}
          />

          {!useSameAddress && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  label="Address Line 1"
                  name="returnAddressLine1"
                  control={control}
                  errors={errors}
                  placeholder="Enter street address"
                  required
                />

                <FormInput
                  label="Address Line 2"
                  name="returnAddressLine2"
                  control={control}
                  errors={errors}
                  placeholder="Apartment, suite, etc. (optional)"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormInput
                  label="City"
                  name="returnCity"
                  control={control}
                  errors={errors}
                  placeholder="Enter city"
                  required
                />

                <FormSelect
                  label="Region"
                  name="returnState"
                  control={control}
                  errors={errors}
                  placeholder="Select region"
                  options={GHANA_REGIONS.map(region => ({ value: region, label: region }))}
                  required
                />

                <FormInput
                  label="Zone"
                  name="returnZone"
                  control={control}
                  errors={errors}
                  placeholder="Enter zone"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormSelect
                  label="Country"
                  name="returnCountry"
                  control={control}
                  errors={errors}
                  options={[{ value: 'Ghana', label: 'Ghana' }]}
                  required
                />

                <FormInput
                  label="Postal Code"
                  name="returnPostalCode"
                  control={control}
                  errors={errors}
                  placeholder="Enter postal code (optional)"
                />

                <FormInput
                  label="Digital GPS"
                  name="returnDigitalGps"
                  control={control}
                  errors={errors}
                  placeholder="Enter GPS address (optional)"
                />
              </div>

              <FormInput
                label="Phone Number"
                name="returnPhone"
                control={control}
                errors={errors}
                placeholder="Enter phone number (optional)"
              />
            </>
          )}
        </FormSection>

        {/* Form Actions */}
        <FormActions>
          {onBack && (
            <Button type="button" variant="outline" onClick={onBack}>
              Back
            </Button>
          )}

          <Button
            type="submit"
            disabled={updateShippingInfoMutation.isPending}
          >
            {updateShippingInfoMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Complete Onboarding'
            )}
          </Button>
        </FormActions>
      </form>
    </FormLayout>
  );
};

export default ShippingInfoForm;
