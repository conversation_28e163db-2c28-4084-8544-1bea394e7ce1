'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useOnboardingStatusQuery, useVerificationStatus } from '@/lib/hooks/use-onboarding';
import { OnboardingStatusResponse, VerificationStatusResponse } from '@/lib/types/auth';

interface StatusContextType {
  // Onboarding Status
  onboardingData: OnboardingStatusResponse['data'] | undefined;
  onboardingLoading: boolean;
  onboardingError: any;
  needsOnboarding: boolean;
  isOnboardingComplete: boolean;
  
  // Verification Status
  verificationData: VerificationStatusResponse['data'] | undefined;
  verificationLoading: boolean;
  verificationError: any;
  isVerified: boolean;
  
  // Combined loading state
  isLoading: boolean;
}

const StatusContext = createContext<StatusContextType | undefined>(undefined);

interface StatusProviderProps {
  children: ReactNode;
}

export const StatusProvider: React.FC<StatusProviderProps> = ({ children }) => {
  // Get onboarding status
  const { 
    data: onboardingResponse, 
    isLoading: onboardingLoading, 
    error: onboardingError 
  } = useOnboardingStatusQuery();

  // Get verification status
  const { 
    data: verificationResponse, 
    isLoading: verificationLoading, 
    error: verificationError 
  } = useVerificationStatus();

  // Extract data
  const onboardingData = onboardingResponse?.data;
  const verificationData = verificationResponse?.data;

  // Calculate derived states
  const needsOnboarding = () => {
    // STRICT ACCESS CONTROL: Use the explicit isComplete flag from API
    if (onboardingData?.isComplete === true) {
      return false; // Only allow access to other pages if explicitly complete
    }
    // If isComplete is false, undefined, or null, user needs onboarding
    return true;
  };

  const isOnboardingComplete = onboardingData?.isComplete === true;
  const isVerified = verificationData?.verificationStatus === 'verified';
  const isLoading = onboardingLoading || verificationLoading;

  const value: StatusContextType = {
    // Onboarding Status
    onboardingData,
    onboardingLoading,
    onboardingError,
    needsOnboarding: needsOnboarding(),
    isOnboardingComplete,
    
    // Verification Status
    verificationData,
    verificationLoading,
    verificationError,
    isVerified,
    
    // Combined loading state
    isLoading,
  };

  return (
    <StatusContext.Provider value={value}>
      {children}
    </StatusContext.Provider>
  );
};

// Custom hook to use the status context
export const useStatus = (): StatusContextType => {
  const context = useContext(StatusContext);
  if (context === undefined) {
    throw new Error('useStatus must be used within a StatusProvider');
  }
  return context;
};

// Convenience hooks for specific status checks
export const useOnboardingStatusFromContext = () => {
  const { onboardingData, onboardingLoading, onboardingError, needsOnboarding, isOnboardingComplete } = useStatus();
  
  const progress = onboardingData?.onboardingProgress;
  
  const getNextStep = () => {
    if (!progress) return 'business-info';
    
    if (!progress.businessInfo) return 'business-info';
    if (!progress.paymentInfo) return 'payment-info';
    if (!progress.shopInfo) return 'shop-info';
    if (!progress.shippingInfo) return 'shipping-info';
    
    return null; // All steps completed
  };

  const getCompletedSteps = () => {
    if (!progress) return [];
    
    const completed = [];
    if (progress.businessInfo) completed.push('business-info');
    if (progress.paymentInfo) completed.push('payment-info');
    if (progress.shopInfo) completed.push('shop-info');
    if (progress.shippingInfo) completed.push('shipping-info');
    
    return completed;
  };

  return {
    isLoading: onboardingLoading,
    error: onboardingError,
    needsOnboarding,
    nextStep: getNextStep(),
    completedSteps: getCompletedSteps(),
    totalSteps: 4, // business info, payment info, shop info, shipping info
    progress,
    onboardingStatus: onboardingData?.onboardingStatus,
    isComplete: isOnboardingComplete,
  };
};

export const useVerificationStatusFromContext = () => {
  const { verificationData, verificationLoading, verificationError, isVerified } = useStatus();
  
  return {
    data: verificationData ? { data: verificationData } : undefined,
    isLoading: verificationLoading,
    error: verificationError,
    isVerified,
  };
};
