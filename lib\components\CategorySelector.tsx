"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"

import { ChevronRight, Search, ArrowLeft } from "lucide-react"
import { useCategoriesHierarchy } from "@/lib/hooks/use-categories"
import { Category } from "@/lib/types/categories"

interface CategorySelectorProps {
  value?: string
  onValueChange: (categoryId: string, categoryName: string) => void
  placeholder?: string
  className?: string
  error?: boolean
  disabled?: boolean
}

interface FlattenedCategory {
  _id: string
  name: string
  path: string
}

export function CategorySelector({
  value,
  onValueChange,
  placeholder = "Select Category",
  className = "",
  error = false,
  disabled = false
}: CategorySelectorProps) {
  const { categories, isLoading: categoriesLoading } = useCategoriesHierarchy()
  
  // Modal state
  const [isModalOpen, setIsModalOpen] = React.useState(false)
  const [currentCategories, setCurrentCategories] = React.useState<Category[]>([])
  const [breadcrumb, setBreadcrumb] = React.useState<string[]>([])
  const [searchTerm, setSearchTerm] = React.useState("")

  // Flatten categories for search functionality
  const flattenCategories = (categories: Category[], path: string[] = []): FlattenedCategory[] => {
    return categories.flatMap((category) => {
      const currentPath = [...path, category.name]

      const result: FlattenedCategory[] = []
      
      // Always include the current category
      result.push({
        _id: category._id,
        name: category.name,
        path: currentPath.join(" > ")
      })

      // If it has children, recursively flatten them
      if (category.immediateChildren && category.immediateChildren.length > 0) {
        result.push(...flattenCategories(category.immediateChildren, currentPath))
      }

      return result
    })
  }

  const allCategories = categories ? flattenCategories(categories) : []

  // Get selected category name for display
  const getSelectedCategoryName = () => {
    if (!value) return placeholder
    const selectedCategory = allCategories.find(cat => cat._id === value)
    return selectedCategory ? selectedCategory.name : placeholder
  }

  // Initialize categories when they load
  React.useEffect(() => {
    if (categories && categories.length > 0 && currentCategories.length === 0) {
      setCurrentCategories(categories)
    }
  }, [categories, currentCategories.length])

  // Filter categories based on search term
  const filteredCategories = React.useMemo(() => {
    if (!searchTerm) {
      return currentCategories
    }
    
    // When searching, show flattened results
    return allCategories.filter(cat =>
      cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cat.path.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [searchTerm, currentCategories, allCategories])

  const openCategoryModal = () => {
    if (disabled) return
    setSearchTerm("")
    setCurrentCategories(categories || [])
    setBreadcrumb([])
    setIsModalOpen(true)
  }

  const selectCategory = (categoryId: string, categoryName: string) => {
    onValueChange(categoryId, categoryName)
    setIsModalOpen(false)
  }

  const goDeeper = (category: Category) => {
    setBreadcrumb(prev => [...prev, category.name])
    setCurrentCategories(category.immediateChildren || [])
    setSearchTerm("") // Clear search when navigating
  }

  const goBack = () => {
    if (breadcrumb.length === 0) return
    
    const newBreadcrumb = breadcrumb.slice(0, -1)
    setBreadcrumb(newBreadcrumb)
    
    // Navigate back through the tree
    let currentLevel = categories || []
    for (const crumb of newBreadcrumb) {
      const category = currentLevel.find(cat => cat.name === crumb)
      if (category && category.immediateChildren) {
        currentLevel = category.immediateChildren
      }
    }
    setCurrentCategories(currentLevel)
  }

  const isSearching = searchTerm.length > 0

  return (
    <>
      <Button
        type="button"
        variant="outline"
        className={`w-full justify-start ${error ? 'border-red-300' : ''} ${className}`}
        onClick={openCategoryModal}
        disabled={disabled}
      >
        {getSelectedCategoryName()}
      </Button>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="w-full max-w-[90%] max-h-[90vh] overflow-y-auto rounded-lg">
          <DialogHeader>
            <DialogTitle>Select Category</DialogTitle>
            <DialogDescription>
              Choose a category for your product. You can browse through subcategories or search for specific categories.
            </DialogDescription>
          </DialogHeader>

          {categoriesLoading ? (
            <div className="text-center py-4">Loading categories...</div>
          ) : (
            <>
              {/* Breadcrumb navigation */}
              {breadcrumb.length > 0 && !isSearching && (
                <div className="mb-4">
                  <Button variant="ghost" size="sm" onClick={goBack} className="mb-2">
                    <ArrowLeft className="w-4 h-4 mr-1" />
                    Back
                  </Button>
                  
                </div>
              )}

              {/* Search input */}
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Categories list */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {isSearching ? (
                  // Search results view
                  filteredCategories.length > 0 ? (
                    (filteredCategories as FlattenedCategory[]).map((cat, index) => (
                      <Button
                        key={`${cat._id}-${index}`}
                        variant="outline"
                        className="w-full text-left justify-start p-3 h-auto"
                        onClick={() => selectCategory(cat._id, cat.name)}
                      >
                        <div className="flex flex-col items-start w-full overflow-hidden">
                          <div className="font-medium truncate w-full">{cat.name}</div>
                          <div className="text-xs text-gray-500 truncate w-full">{cat.name}</div>
                        </div>
                      </Button>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      No categories found matching "{searchTerm}"
                    </div>
                  )
                ) : (
                  // Tree navigation view
                  currentCategories.map((cat, index) => (
                    <div key={`${cat._id}-${index}`} className="space-y-1">
                      {cat.immediateChildren && cat.immediateChildren.length > 0 ? (
                        // Category with subcategories - both navigable and selectable
                        <div className="border rounded-lg p-2 bg-gray-50">
                          <div className="flex items-center justify-between mb-2 overflow-hidden">
                            <div className="flex flex-col min-w-0 flex-1">
                              <div className="font-medium truncate">{cat.description}</div>
                              <div className="text-xs text-gray-500 whitespace-nowrap">
                                {cat.childrenCount} subcategories
                              </div>
                            </div>
                          </div>
                          <div className="overflow-x-auto">
                            <div className="flex gap-2 min-w-fit">
                              {/* <Button
                                variant="outline"
                                size="sm"
                                className="flex-1 whitespace-nowrap"
                                onClick={() => selectCategory(cat._id, cat.name)}
                              >
                                Select "{cat.name}"
                              </Button> */}
                              <Button
                                variant="ghost"
                                size="sm"
                                className="flex-1 whitespace-nowrap"
                                onClick={() => goDeeper(cat)}
                              >
                                Browse subcategories
                                <ChevronRight className="w-4 h-4 ml-1 flex-shrink-0" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        // Leaf category - selectable only
                        <Button
                          variant="outline"
                          className="w-full text-left justify-start p-3 h-auto"
                          onClick={() => selectCategory(cat._id, cat.name)}
                        >
                          <div className="flex flex-col items-start w-full overflow-hidden">
                            <div className="font-medium truncate w-full">{cat.description}</div>
                            <div className="text-xs text-green-600 whitespace-nowrap">
                              ✓ Final category
                            </div>
                          </div>
                        </Button>
                      )}
                    </div>
                  ))
                )}
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
