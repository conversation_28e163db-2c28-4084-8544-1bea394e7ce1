"use client";

import { useSearchParams } from "next/navigation";
import { Suspense } from "react";
import ProfileLayout from "./_components/ProfileLayout";
import { BusinessInfoForm, PaymentInfoForm, ShopInfoForm, ShippingInfoForm } from "@/lib/components/shared/forms";

const ProfilePageContent = () => {
  const searchParams = useSearchParams();
  const currentStep = searchParams.get("step") || "shop-info";

  const renderCurrentStep = () => {
    switch (currentStep) {
      case "shop-info":
        return <ShopInfoForm isProfileMode={false} />;
      case "business-info":
        return <BusinessInfoForm isProfileMode={false} />;
      case "payment-info":
        return <PaymentInfoForm isProfileMode={false} />;
      case "shipping-info":
        return <ShippingInfoForm isProfileMode={false} />;
      default:
        return <ShopInfoForm isProfileMode={false} />;
    }
  };

  return (
    <ProfileLayout>
      {renderCurrentStep()}
    </ProfileLayout>
  );
};

const ProfilePage = () => {
  return (
    <Suspense fallback={<div>Loading profile...</div>}>
      <ProfilePageContent />
    </Suspense>
  );
};

export default ProfilePage;