/**
 * Search API Module
 * Handles search functionality for products, bales, and shops
 */

import { BaseApiClient } from './base-client';
import { API_ENDPOINTS } from './config';
import { handleApiError as handleError } from './errors';
import type { BaseApiResponse } from './types';
import type { SearchFilters, SearchResponse } from '@/lib/hooks/use-search';

// Create API client instance
const apiClient = new BaseApiClient();

// Search API functions
export const searchApi = {
  /**
   * Search for products, bales, and shops
   */
  search: async (filters: SearchFilters): Promise<SearchResponse> => {
    try {
      // Build query string
      const queryParams = new URLSearchParams();

      // Add search parameters
      if (filters.q) queryParams.append('q', filters.q);
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.brands) queryParams.append('brands', filters.brands);
      if (filters.sizes) queryParams.append('sizes', filters.sizes);
      if (filters.gender) queryParams.append('gender', filters.gender);
      if (filters.countries) queryParams.append('countries', filters.countries);
      if (filters.conditions) queryParams.append('conditions', filters.conditions);
      if (filters.minPrice) queryParams.append('minPrice', filters.minPrice);
      if (filters.maxPrice) queryParams.append('maxPrice', filters.maxPrice);
      if (filters.sort) queryParams.append('sort', filters.sort);
      if (filters.page) queryParams.append('page', filters.page.toString());
      if (filters.limit) queryParams.append('limit', filters.limit.toString());

      const endpoint = `${API_ENDPOINTS.SEARCH.GENERAL}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

      // Use authenticated request if user is logged in, otherwise public request
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;

      if (token) {
        return await apiClient.get<SearchResponse>(endpoint);
      } else {
        return await apiClient.publicGet<SearchResponse>(endpoint);
      }
    } catch (error) {
      console.error('Error performing search:', error);
      throw handleError(error);
    }
  },

  /**
   * Get search suggestions (if needed in the future)
   */
  getSuggestions: async (query: string): Promise<BaseApiResponse<{ suggestions: string[] }>> => {
    try {
      const endpoint = `${API_ENDPOINTS.SEARCH.SUGGESTIONS}?q=${encodeURIComponent(query)}`;
      return await apiClient.publicGet<BaseApiResponse<{ suggestions: string[] }>>(endpoint);
    } catch (error) {
      console.error('Error fetching search suggestions:', error);
      throw handleError(error);
    }
  },

  /**
   * Get trending searches (if needed in the future)
   */
  getTrendingSearches: async (): Promise<BaseApiResponse<{ trending: string[] }>> => {
    try {
      const endpoint = API_ENDPOINTS.SEARCH.TRENDING;
      return await apiClient.publicGet<BaseApiResponse<{ trending: string[] }>>(endpoint);
    } catch (error) {
      console.error('Error fetching trending searches:', error);
      throw handleError(error);
    }
  },
};

// Error handler for search API
export const handleSearchApiError = (error: any): string => {
  const apiError = handleError(error);
  return typeof apiError === 'string' ? apiError : apiError.message || 'An error occurred during search';
};
