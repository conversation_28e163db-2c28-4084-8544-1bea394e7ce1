"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Star, Users } from 'lucide-react';
import Carousel from 'react-multi-carousel';
import 'react-multi-carousel/lib/styles.css';
import type { Shop } from '@/lib/hooks/use-search';

interface ShopsListProps {
  shops: Shop[];
  loading?: boolean;
  error?: string | null;
}

// Carousel responsive configuration for shops
const responsive = {
  superLargeDesktop: {
    breakpoint: { max: 4000, min: 3000 },
    items: 5
  },
  desktop: {
    breakpoint: { max: 3000, min: 1024 },
    items: 4
  },
  tablet: {
    breakpoint: { max: 1024, min: 464 },
    items: 3
  },
  mobile: {
    breakpoint: { max: 464, min: 0 },
    items: 2
  }
};

const ShopCard: React.FC<{ shop: Shop }> = ({ shop }) => {
  const { metrics } = shop;

  return (
    <Link href={`/shops/${shop._id}`} className="block">
      <div className="relative bg-white shadow-md rounded-lg overflow-hidden w-full">
        {/* Shop Logo */}
        <div className="relative w-full h-48 bg-gray-100 flex items-center justify-center">
          <Image
            src={shop.logo || '/placeholder-shop.png'}
            alt={shop.name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
          />
        </div>

        {/* Quality Score Badge */}
        {metrics.qualityScore > 0 && (
          <div className="absolute top-2 right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded">
            {metrics.qualityScore}%
          </div>
        )}

        {/* Shop Details */}
        <div className="p-2">
          <h2 className="text-sm font-semibold truncate mb-1">{shop.name}</h2>

          {/* Rating and Followers */}
          <div className="flex items-center gap-2 mb-2">
            <div className="flex items-center gap-1">
              <Star className="w-3 h-3 text-yellow-400 fill-current" />
              <span className="text-xs font-medium">
                {metrics.averageRating > 0 ? metrics.averageRating.toFixed(1) : 'New'}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <Users className="w-3 h-3 text-blue-500" />
              <span className="text-xs text-gray-600">
                {shop.followers > 0 ? `${shop.followers}` : '0'}
              </span>
            </div>
          </div>

          {/* Products and Bales Count */}
          <div className="flex items-center gap-1 mt-1">
            <span className="text-xs text-gray-500">Items:</span>
            <div className="flex gap-1 flex-wrap">
              <Badge variant="outline" className="text-xs px-1 py-0 h-4">
                {metrics.totalProducts}P
              </Badge>
              {metrics.totalBales > 0 && (
                <Badge variant="outline" className="text-xs px-1 py-0 h-4">
                  {metrics.totalBales}B
                </Badge>
              )}
            </div>
          </div>

          {/* Sales Count */}
          {metrics.totalSales > 0 && (
            <p className="text-xs font-semibold text-green-600 mt-1">
              {metrics.totalSales} sales
            </p>
          )}
        </div>
      </div>
    </Link>
  );
};

const ShopsListSkeleton: React.FC = () => (
  <Carousel responsive={responsive} infinite={false} autoPlay={false} itemClass="px-2">
    {[...Array(4)].map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="relative bg-white shadow-md rounded-lg overflow-hidden w-full">
          <div className="w-full h-48 bg-gray-200"></div>
          <div className="p-2 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="flex space-x-2">
              <div className="h-3 bg-gray-200 rounded w-12"></div>
              <div className="h-3 bg-gray-200 rounded w-12"></div>
            </div>
            <div className="flex space-x-1">
              <div className="h-4 w-8 bg-gray-200 rounded"></div>
              <div className="h-4 w-8 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    ))}
  </Carousel>
);

const ShopsList: React.FC<ShopsListProps> = ({ shops, loading = false, error = null }) => {
  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Shops</h2>
        <ShopsListSkeleton />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Shops</h2>
        <div className="text-center py-8">
          <p className="text-red-600 mb-2">Error loading shops</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!shops || shops.length === 0) {
    return null; // Don't show anything if no shops
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">
          Shops ({shops.length})
        </h2>
      </div>

      {/* Shops Carousel */}
      <Carousel
        responsive={responsive}
        infinite={false}
        autoPlay={false}
        
        itemClass="px-2"
        containerClass="shops-carousel"
      >
        {shops.map((shop) => (
          <ShopCard key={shop._id} shop={shop} />
        ))}
      </Carousel>
    </div>
  );
};

export default ShopsList;
