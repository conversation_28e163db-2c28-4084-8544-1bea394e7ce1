"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Users, Package, TrendingUp } from 'lucide-react';
import type { Shop } from '@/lib/hooks/use-search';

interface ShopsListProps {
  shops: Shop[];
  loading?: boolean;
  error?: string | null;
}

const ShopCard: React.FC<{ shop: Shop }> = ({ shop }) => {
  const { metrics } = shop;
  
  return (
    <Link href={`/shops/${shop._id}`} className="block">
      <Card className="hover:shadow-md transition-shadow duration-200 h-full">
        <CardContent className="p-4">
          <div className="flex items-start space-x-4">
            {/* Shop Logo */}
            <div className="relative w-16 h-16 flex-shrink-0">
              <Image
                src={shop.logo || '/placeholder-shop.png'}
                alt={shop.name}
                fill
                className="object-cover rounded-lg"
                sizes="64px"
              />
            </div>

            {/* Shop Info */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg text-gray-900 truncate mb-1">
                {shop.name}
              </h3>
              
              {shop.description && (
                <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                  {shop.description}
                </p>
              )}

              {/* Metrics Row 1 */}
              <div className="flex items-center space-x-4 mb-2">
                {/* Average Rating */}
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium">
                    {metrics.averageRating > 0 ? metrics.averageRating.toFixed(1) : 'New'}
                  </span>
                </div>

                {/* Quality Score */}
                {metrics.qualityScore > 0 && (
                  <div className="flex items-center space-x-1">
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium text-green-600">
                      {metrics.qualityScore}%
                    </span>
                  </div>
                )}

                {/* Followers */}
                <div className="flex items-center space-x-1">
                  <Users className="w-4 h-4 text-blue-500" />
                  <span className="text-sm text-gray-600">
                    {shop.followers > 0 ? `${shop.followers}` : '0'}
                  </span>
                </div>
              </div>

              {/* Metrics Row 2 */}
              <div className="flex items-center space-x-3">
                {/* Total Products */}
                <div className="flex items-center space-x-1">
                  <Package className="w-4 h-4 text-gray-400" />
                  <span className="text-xs text-gray-500">
                    {metrics.totalProducts} products
                  </span>
                </div>

                {/* Total Bales */}
                {metrics.totalBales > 0 && (
                  <div className="flex items-center space-x-1">
                    <Package className="w-4 h-4 text-gray-400" />
                    <span className="text-xs text-gray-500">
                      {metrics.totalBales} bales
                    </span>
                  </div>
                )}

                {/* Total Sales */}
                {metrics.totalSales > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {metrics.totalSales} sales
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

const ShopsListSkeleton: React.FC = () => (
  <div className="space-y-4">
    {[...Array(3)].map((_, index) => (
      <Card key={index} className="animate-pulse">
        <CardContent className="p-4">
          <div className="flex items-start space-x-4">
            <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0"></div>
            <div className="flex-1 space-y-2">
              <div className="h-5 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-full"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              <div className="flex space-x-4">
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

const ShopsList: React.FC<ShopsListProps> = ({ shops, loading = false, error = null }) => {
  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Shops</h2>
        <ShopsListSkeleton />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Shops</h2>
        <div className="text-center py-8">
          <p className="text-red-600 mb-2">Error loading shops</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!shops || shops.length === 0) {
    return null; // Don't show anything if no shops
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">
          Shops ({shops.length})
        </h2>
      </div>
      
      <div className="space-y-4">
        {shops.map((shop) => (
          <ShopCard key={shop._id} shop={shop} />
        ))}
      </div>
    </div>
  );
};

export default ShopsList;
