// Color mapping utility for converting color names to CSS colors
export const COLOR_MAP: Record<string, string> = {
  // Basic colors
  'Black': '#000000',
  'White': '#FFFFFF',
  'Red': '#EF4444',
  'Blue': '#3B82F6',
  'Green': '#10B981',
  'Yellow': '#F59E0B',
  'Pink': '#EC4899',
  'Purple': '#8B5CF6',
  'Orange': '#F97316',
  'Brown': '#A16207',
  'Gray': '#6B7280',
  'Grey': '#6B7280',
  
  // Extended colors
  'Navy': '#1E3A8A',
  'Beige': '#F5F5DC',
  'Khaki': '#F0E68C',
  'Maroon': '#800000',
  'Teal': '#14B8A6',
  'Lime': '#84CC16',
  'Indigo': '#6366F1',
  'Violet': '#7C3AED',
  'Rose': '#F43F5E',
  'Emerald': '#059669',
  'Cyan': '#06B6D4',
  'Sky': '#0EA5E9',
  'Amber': '#D97706',
  'Slate': '#475569',
  'Zinc': '#52525B',
  'Stone': '#78716C',
  'Neutral': '#525252',
  
  // Fashion-specific colors
  'Cream': '#FFFDD0',
  'Ivory': '#FFFFF0',
  'Charcoal': '#36454F',
  'Burgundy': '#800020',
  'Coral': '#FF7F50',
  'Turquoise': '#40E0D0',
  'Lavender': '#E6E6FA',
  'Mint': '#98FB98',
  'Peach': '#FFCBA4',
  'Salmon': '#FA8072',
  'Gold': '#FFD700',
  'Silver': '#C0C0C0',
  'Bronze': '#CD7F32',
  'Copper': '#B87333',
  'Olive': '#808000',
  'Mustard': '#FFDB58',
  'Rust': '#B7410E',
  'Sage': '#9CAF88',
  'Mauve': '#E0B0FF',
  'Taupe': '#483C32',
};

/**
 * Get CSS color value from color name
 * @param colorName - The color name to convert
 * @returns CSS color value or fallback color
 */
export const getColorValue = (colorName: string): string => {
  // Normalize the color name (capitalize first letter, handle case variations)
  const normalizedName = colorName.charAt(0).toUpperCase() + colorName.slice(1).toLowerCase();
  
  return COLOR_MAP[normalizedName] || COLOR_MAP[colorName] || '#6B7280'; // Default to gray
};

/**
 * Check if a color is light (for determining text color)
 * @param color - CSS color value
 * @returns true if color is light, false if dark
 */
export const isLightColor = (color: string): boolean => {
  // Convert hex to RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  
  return luminance > 0.5;
};

/**
 * Get appropriate text color for a background color
 * @param backgroundColor - CSS background color
 * @returns CSS color for text (black or white)
 */
export const getTextColor = (backgroundColor: string): string => {
  return isLightColor(backgroundColor) ? '#000000' : '#FFFFFF';
};
