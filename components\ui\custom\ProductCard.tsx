"use client"
import { useRouter } from 'next/navigation';
import React, { memo, useCallback } from 'react';
import { getColorValue } from '@/lib/utils/colors';
import { Badge } from '@/components/ui/badge';

interface Product {
    id: string;
    image: string;
    name: string;
    price: number;
    discount?: number;
    itemsLeft?: number;
    availableColors?: string[];
    availableSizes?: string[];
  }

  interface ProductCardProps {
    product: Product;
    cardType: 'flashy' | 'normal' | 'withAddToCart';
  }


  const ProductCard: React.FC<ProductCardProps> = memo(({ product, cardType }) => {
    const { id, image, name, price, discount, itemsLeft, availableColors, availableSizes } = product;
    const router = useRouter();

    const onClick = useCallback(() => {
      router.push(`/items/${id}`)
    }, [id, router]);

    return (
      <div className="relative bg-white shadow-md rounded-lg overflow-hidden w-full">
        {/* Product Image */}
        <img src={image} alt={name} className="w-full h-48 object-cover" onClick={onClick} />

        {/* Discount Badge */}
        {discount && (
          <div className="absolute top-2 right-2 bg-white/60 text-primary text-xs font-bold px-2 py-1 rounded">
            -{discount}%
          </div>
        )}

        {/* Product Details */}
        <div className="p-2" onClick={onClick}>
          <h2 className="text-sm truncate">{name}</h2>
          <div className="flex gap-1 items-center flex-wrap">
          {discount ? (
            <>
              <p className="text-lg font-semibold">GH₵{price}</p>
              <p className="text-sm text-gray-500 line-through">
              GH₵ {(price / (1 - discount / 100)).toFixed(2)}
            </p>
            </>
          ) : (
            <p className="text-lg font-bold text-gray-800">GH₵ {price}</p>
          )}
          </div>

          {/* Available Colors */}
          {availableColors && availableColors.length > 0 && (
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs text-gray-500">Colors:</span>
              <div className="flex gap-1">
                {availableColors.slice(0, 4).map((color, index) => (
                  <div
                    key={index}
                    className="w-3 h-3 rounded-full border border-gray-300"
                    style={{ backgroundColor: getColorValue(color) }}
                    title={color}
                  />
                ))}
                {availableColors.length > 4 && (
                  <span className="text-xs text-gray-500">+{availableColors.length - 4}</span>
                )}
              </div>
            </div>
          )}

          {/* Available Sizes */}
          {availableSizes && availableSizes.length > 0 && (
            <div className="flex items-center gap-1 mt-1">
              <span className="text-xs text-gray-500">Sizes:</span>
              <div className="flex gap-1 flex-wrap">
                {availableSizes.slice(0, 3).map((size, index) => (
                  <Badge key={index} variant="outline" className="text-xs px-1 py-0 h-4">
                    {size}
                  </Badge>
                ))}
                {availableSizes.length > 3 && (
                  <span className="text-xs text-gray-500">+{availableSizes.length - 3}</span>
                )}
              </div>
            </div>
          )}

          {/* Items Left */}
          {itemsLeft !== undefined && (
            <p className={`text-xs font-semibold mt-1 ${itemsLeft > 0 ? 'text-gray-500' : 'text-red-500'}`}>
              {itemsLeft > 0 ? `${itemsLeft} items left` : 'Out of stock'}
            </p>
          )}
        </div>

        <div className="p-2">
          {/* Conditional Add to Cart Button */}
          {cardType === 'withAddToCart' && itemsLeft !== undefined && itemsLeft > 0 && (
            <button className="mt-2 w-full bg-primary text-white text-sm font-bold py-1 px-2 rounded hover:bg-primary/90 transition">
              Add to Cart
            </button>
          )}
        </div>

      </div>
    );
  });

  ProductCard.displayName = 'ProductCard';

  export default ProductCard;