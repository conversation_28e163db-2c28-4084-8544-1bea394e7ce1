'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import {
  ProductInfoFormData,
  BaleInfoFormData,
  ProductSpecificationsFormData,
  BaleSpecificationsFormData,
  ProductVariationsFormData,
  BaleVariationsFormData,
  CreateProductData,
  CreateBaleData,
  CreateProductOrBaleData,
  ProductType
} from '@/lib/types/products';

interface ProductFormContextType {
  // Current type
  currentType: ProductType;
  setCurrentType: (type: ProductType) => void;

  // Form data (union types to handle both products and bales)
  productInfo: ProductInfoFormData | BaleInfoFormData;
  specifications: ProductSpecificationsFormData | BaleSpecificationsFormData;
  variations: ProductVariationsFormData | BaleVariationsFormData;

  // Update functions
  updateProductInfo: (data: Partial<ProductInfoFormData | BaleInfoFormData>) => void;
  updateSpecifications: (data: Partial<ProductSpecificationsFormData | BaleSpecificationsFormData>) => void;
  updateVariations: (data: Partial<ProductVariationsFormData | BaleVariationsFormData>) => void;

  // Utility functions
  getCompleteFormData: () => CreateProductOrBaleData;
  resetForm: () => void;
  isStepComplete: (step: 'product-info' | 'specifications' | 'variations') => boolean;

  // Validation
  validateProductInfo: () => string[];
  validateSpecifications: () => string[];
  validateVariations: () => string[];
}

const ProductFormContext = createContext<ProductFormContextType | undefined>(undefined);

// Initial form data for products
const initialProductInfo: ProductInfoFormData = {
  type: 'product',
  name: '',
  brand: '',
  description: '',
  basePrice: 0,
  category: '',
  gender: 'Unisex',
  productImages: [],
  highlights: [],
  tags: [],
};

// Initial form data for bales
const initialBaleInfo: BaleInfoFormData = {
  type: 'bale',
  name: '',
  description: '',
  basePrice: 0,
  category: '',
  country: '',
  totalItems: 1,
  weight: 0,
  condition: 'Good',
  gender: 'Unisex',
  productImages: [],
  highlights: [],
  tags: [],
};

const initialProductSpecifications: ProductSpecificationsFormData = {
  mainMaterial: '',
  dressStyle: '',
  pantType: '',
  skirtType: '',
  mensPantSize: '',
  fitType: undefined,
  pattern: '',
  closure: '',
  neckline: '',
  sleeveLength: '',
  waistline: '',
  hemline: '',
};

const initialBaleSpecifications: BaleSpecificationsFormData = {
  mainMaterial: '',
  dressStyle: '',
  pantType: '',
  skirtType: '',
  mensPantSize: '',
  fitType: undefined,
  pattern: '',
  closure: '',
  neckline: '',
  sleeveLength: '',
  waistline: '',
  hemline: '',
};

const initialProductVariations: ProductVariationsFormData = {
  variations: [{
    color: '',
    size: '',
    quantity: 1,
    price: 0,
    salePrice: undefined,
    saleStartDate: '',
    saleEndDate: '',
  }],
  relatedCategories: [],
};

const initialBaleVariations: BaleVariationsFormData = {
  variations: [{
    identifier: '',
    quantity: 1,
    price: 0,
  }],
  relatedCategories: [],
};

export function ProductFormProvider({ children }: { children: ReactNode }) {
  const [currentType, setCurrentType] = useState<ProductType>('product');
  const [productInfo, setProductInfo] = useState<ProductInfoFormData | BaleInfoFormData>(initialProductInfo);
  const [specifications, setSpecifications] = useState<ProductSpecificationsFormData | BaleSpecificationsFormData>(initialProductSpecifications);
  const [variations, setVariations] = useState<ProductVariationsFormData | BaleVariationsFormData>(initialProductVariations);

  // Reset form data when type changes
  const handleTypeChange = useCallback((type: ProductType) => {
    setCurrentType(type);
    if (type === 'product') {
      setProductInfo(initialProductInfo);
      setSpecifications(initialProductSpecifications);
      setVariations(initialProductVariations);
    } else {
      setProductInfo(initialBaleInfo);
      setSpecifications(initialBaleSpecifications);
      setVariations(initialBaleVariations);
    }
  }, []);

  const updateProductInfo = useCallback((data: Partial<ProductInfoFormData | BaleInfoFormData>) => {
    
    setProductInfo(prev => {
      const updated = { ...prev, ...data } as ProductInfoFormData | BaleInfoFormData;
      
      return updated;
    });
  }, []);

  const updateSpecifications = useCallback((data: Partial<ProductSpecificationsFormData | BaleSpecificationsFormData>) => {
    
    setSpecifications(prev => {
      const updated = { ...prev, ...data } as ProductSpecificationsFormData | BaleSpecificationsFormData;
      
      return updated;
    });
  }, []);

  const updateVariations = useCallback((data: Partial<ProductVariationsFormData | BaleVariationsFormData>) => {
  
    setVariations(prev => {
      const updated = { ...prev, ...data } as ProductVariationsFormData | BaleVariationsFormData;
      
      return updated;
    });
  }, []);

  const getCompleteFormData = useCallback((): CreateProductOrBaleData => {
    

    if (currentType === 'product') {
      const productInfoData = productInfo as ProductInfoFormData;
      const productSpecsData = specifications as ProductSpecificationsFormData;
      const productVarsData = variations as ProductVariationsFormData;

      const completeData: CreateProductData = {
        type: 'product',
        name: productInfoData.name,
        brand: productInfoData.brand,
        description: productInfoData.description,
        basePrice: productInfoData.basePrice,
        category: productInfoData.category,
        gender: productInfoData.gender,
        productImages: productInfoData.productImages,
        highlights: productInfoData.highlights,
        tags: productInfoData.tags,
        specifications: productSpecsData,
        variations: productVarsData.variations,
        relatedCategories: productVarsData.relatedCategories,
      };

     
      return completeData;
    } else {
      const baleInfoData = productInfo as BaleInfoFormData;
      const baleSpecsData = specifications as BaleSpecificationsFormData;
      const baleVarsData = variations as BaleVariationsFormData;

      const completeData: CreateBaleData = {
        type: 'bale',
        name: baleInfoData.name,
        description: baleInfoData.description,
        basePrice: baleInfoData.basePrice,
        country: baleInfoData.country,
        totalItems: baleInfoData.totalItems,
        weight: baleInfoData.weight,
        condition: baleInfoData.condition,
        gender: baleInfoData.gender,
        dimensions: baleInfoData.dimensions,
        productImages: baleInfoData.productImages,
        highlights: baleInfoData.highlights,
        tags: baleInfoData.tags,
        specifications: baleSpecsData,
        variations: baleVarsData.variations,
        relatedCategories: baleVarsData.relatedCategories,
      };

      
      return completeData;
    }
  }, [currentType, productInfo, specifications, variations]);

  const resetForm = useCallback(() => {
    if (currentType === 'product') {
      setProductInfo(initialProductInfo);
      setSpecifications(initialProductSpecifications);
      setVariations(initialProductVariations);
    } else {
      setProductInfo(initialBaleInfo);
      setSpecifications(initialBaleSpecifications);
      setVariations(initialBaleVariations);
    }
  }, [currentType]);

  const isStepComplete = useCallback((step: 'product-info' | 'specifications' | 'variations'): boolean => {
    switch (step) {
      case 'product-info':
        if (currentType === 'product') {
          const productInfoData = productInfo as ProductInfoFormData;
          return !!(
            productInfoData.name &&
            productInfoData.brand &&
            productInfoData.description &&
            productInfoData.basePrice > 0 &&
            productInfoData.category &&
            productInfoData.gender &&
            productInfoData.productImages.length > 0
          );
        } else {
          const baleInfoData = productInfo as BaleInfoFormData;
          return !!(
            baleInfoData.name &&
            baleInfoData.description &&
            baleInfoData.basePrice > 0 &&
            baleInfoData.country &&
            baleInfoData.totalItems > 0 &&
            baleInfoData.weight > 0 &&
            baleInfoData.condition &&
            baleInfoData.productImages.length > 0
          );
        }
      case 'specifications':
        if (currentType === 'product') {
          const productSpecsData = specifications as ProductSpecificationsFormData;
          return !!(
            productSpecsData.mainMaterial ||
            productSpecsData.fitType ||
            productSpecsData.dressStyle ||
            productSpecsData.pantType ||
            productSpecsData.skirtType ||
            productSpecsData.mensPantSize ||
            productSpecsData.pattern ||
            productSpecsData.closure ||
            productSpecsData.neckline ||
            productSpecsData.sleeveLength ||
            productSpecsData.waistline ||
            productSpecsData.hemline
          );
        } else {
          const baleSpecsData = specifications as BaleSpecificationsFormData;
          return !!(
            baleSpecsData.mainMaterial ||
            baleSpecsData.fitType ||
            baleSpecsData.dressStyle ||
            baleSpecsData.pantType ||
            baleSpecsData.skirtType ||
            baleSpecsData.mensPantSize ||
            baleSpecsData.pattern ||
            baleSpecsData.closure ||
            baleSpecsData.neckline ||
            baleSpecsData.sleeveLength ||
            baleSpecsData.waistline ||
            baleSpecsData.hemline
          );
        }
      case 'variations':
        if (currentType === 'product') {
          const productVarsData = variations as ProductVariationsFormData;
          return productVarsData.variations.length > 0 &&
                 productVarsData.variations.every(v => v.color && v.size && v.quantity > 0 && v.price > 0);
        } else {
          const baleVarsData = variations as BaleVariationsFormData;
          return baleVarsData.variations.length > 0 &&
                 baleVarsData.variations.every(v => v.identifier && v.quantity > 0 && v.price > 0);
        }
      default:
        return false;
    }
  }, [currentType, productInfo, specifications, variations]);

  const validateProductInfo = useCallback((): string[] => {
    const errors: string[] = [];

    if (!productInfo.name.trim()) errors.push('Name is required');
    if (!productInfo.description.trim()) errors.push('Description is required');
    if (productInfo.basePrice <= 0) errors.push('Base price must be greater than 0');
    if (productInfo.productImages.length === 0) errors.push('At least one image is required');

    if (currentType === 'product') {
      const productInfoData = productInfo as ProductInfoFormData;
      if (!productInfoData.brand.trim()) errors.push('Brand is required');
      if (!productInfoData.category) errors.push('Category is required');
      if (!productInfoData.gender) errors.push('Gender is required');
    } else {
      const baleInfoData = productInfo as BaleInfoFormData;
      if (!baleInfoData.country.trim()) errors.push('Country is required');
      if (baleInfoData.totalItems <= 0) errors.push('Total items must be greater than 0');
      if (baleInfoData.weight <= 0) errors.push('Weight must be greater than 0');
      if (!baleInfoData.condition) errors.push('Condition is required');
    }

    return errors;
  }, [currentType, productInfo]);

  const validateSpecifications = useCallback((): string[] => {
    const errors: string[] = [];

    // Specifications are generally optional, but we can add specific validation if needed
    // For now, we'll just ensure at least one specification is provided
    const hasAnySpec = !!(
      specifications.mainMaterial ||
      specifications.fitType ||
      specifications.dressStyle ||
      specifications.pantType ||
      specifications.skirtType ||
      specifications.mensPantSize ||
      specifications.pattern ||
      specifications.closure ||
      specifications.neckline ||
      specifications.sleeveLength ||
      specifications.waistline ||
      specifications.hemline
    );

    if (!hasAnySpec) {
      errors.push('At least one specification is required');
    }

    return errors;
  }, [specifications]);

  const validateVariations = useCallback((): string[] => {
    const errors: string[] = [];

    if (variations.variations.length === 0) {
      errors.push('At least one product variation is required');
      return errors;
    }

    if (currentType === 'product') {
      const productVarsData = variations as ProductVariationsFormData;
      productVarsData.variations.forEach((variation, index) => {
        if (!variation.color.trim()) {
          errors.push(`Variation ${index + 1}: Color is required`);
        }
        if (!variation.size.trim()) {
          errors.push(`Variation ${index + 1}: Size is required`);
        }
        if (variation.quantity <= 0) {
          errors.push(`Variation ${index + 1}: Quantity must be greater than 0`);
        }
        if (variation.price <= 0) {
          errors.push(`Variation ${index + 1}: Price must be greater than 0`);
        }
        if (variation.salePrice && variation.salePrice >= variation.price) {
          errors.push(`Variation ${index + 1}: Sale price must be less than regular price`);
        }
      });
    } else {
      const baleVarsData = variations as BaleVariationsFormData;
      baleVarsData.variations.forEach((variation, index) => {
        if (!variation.identifier.trim()) {
          errors.push(`Variation ${index + 1}: Identifier is required`);
        }
        if (variation.quantity <= 0) {
          errors.push(`Variation ${index + 1}: Quantity must be greater than 0`);
        }
        if (variation.price <= 0) {
          errors.push(`Variation ${index + 1}: Price must be greater than 0`);
        }
      });
    }

    return errors;
  }, [currentType, variations]);

  const value: ProductFormContextType = {
    currentType,
    setCurrentType: handleTypeChange,
    productInfo,
    specifications,
    variations,
    updateProductInfo,
    updateSpecifications,
    updateVariations,
    getCompleteFormData,
    resetForm,
    isStepComplete,
    validateProductInfo,
    validateSpecifications,
    validateVariations,
  };

  return (
    <ProductFormContext.Provider value={value}>
      {children}
    </ProductFormContext.Provider>
  );
}

export function useProductForm() {
  const context = useContext(ProductFormContext);
  if (context === undefined) {
    throw new Error('useProductForm must be used within a ProductFormProvider');
  }
  return context;
}
