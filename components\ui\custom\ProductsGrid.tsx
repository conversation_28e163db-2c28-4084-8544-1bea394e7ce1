"use client";

import React from 'react';
import ProductCard from './ProductCard';
import LottieLoader from '@/components/ui/LottieLoader';
import type { Product, Bale } from '@/lib/hooks/use-search';

interface ProductsGridProps {
  products: (Product | Bale)[];
  loading?: boolean;
  error?: string | null;
  showEmptyState?: boolean; // New prop to control whether to show empty state
}

// Helper function to convert search API product to ProductCard format
const convertSearchProductToCardFormat = (product: Product | Bale) => {
  const hasDiscount = product.hasAnyDiscount && product.maxDiscountPercentage > 0;
  const displayPrice = hasDiscount
    ? product.formattedPriceRange.discounted?.min || product.formattedPriceRange.original.min
    : product.formattedPriceRange.original.min;

  return {
    id: product._id,
    image: product.images[0] || '/images/placeholder.jpg',
    name: product.name,
    price: displayPrice,
    discount: hasDiscount ? product.maxDiscountPercentage : undefined,
    itemsLeft: product.totalItemsLeft || 0,
    availableColors: product.availableColors || [],
    availableSizes: product.availableSizes || [],
  };
};

const ProductsGridSkeleton: React.FC = () => (
  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
    {[...Array(8)].map((_, index) => (
      <div key={index} className="animate-pulse">
        <div className="bg-gray-200 aspect-square rounded-lg mb-3"></div>
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="flex space-x-1">
            <div className="h-3 w-3 bg-gray-200 rounded-full"></div>
            <div className="h-3 w-3 bg-gray-200 rounded-full"></div>
            <div className="h-3 w-3 bg-gray-200 rounded-full"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
);

const ProductsGrid: React.FC<ProductsGridProps> = ({
  products,
  loading = false,
  error = null,
  showEmptyState = true
}) => {
  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Products</h2>
        <ProductsGridSkeleton />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Products</h2>
        <div className="text-center py-8">
          <p className="text-red-600 mb-2">Error loading products</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  if (!products || products.length === 0) {
    if (!showEmptyState) {
      // Don't show anything if parent will handle empty state
      return null;
    }

    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Products</h2>
        <div className="text-center py-8">
          <p className="text-gray-500 mb-2">No products found</p>
          <p className="text-sm text-gray-400">
            Try adjusting your search or filters
          </p>
        </div>
      </div>
    );
  }

  // Convert products to card format
  const cardProducts = products.map(convertSearchProductToCardFormat);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">
          Products ({products.length})
        </h2>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
        {cardProducts.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            cardType="normal"
          />
        ))}
      </div>
    </div>
  );
};

export default ProductsGrid;
