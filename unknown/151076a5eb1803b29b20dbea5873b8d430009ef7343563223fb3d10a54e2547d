'use client';

import React, { Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import FullScreenLoader from '@/components/ui/FullScreenLoader';
import VerificationGuard from '@/lib/components/VerificationGuard';

import ProductCreationLayout from './_components/ProductCreationLayout';
import TypeSelectionForm from './_components/TypeSelectionForm';
import ProductInfoForm from './_components/ProductInfoForm';
import SpecificationsForm from './_components/SpecificationsForm';
import VariationsForm from './_components/VariationsForm';

const ProductCreationPageContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentStep = searchParams.get("step") || "type-selection";

  const handleStepComplete = (nextStep?: string) => {
    if (nextStep) {
      router.push(`/creators/products/add?step=${nextStep}`);
    } else {
      // Product creation complete, redirect to products list
      router.push('/creators/products');
    }
  };

  const handleStepBack = (previousStep: string) => {
    router.push(`/creators/products/add?step=${previousStep}`);
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'type-selection':
        return (
          <TypeSelectionForm
            onNext={() => handleStepComplete('product-info')}
          />
        );
      case 'product-info':
        return (
          <ProductInfoForm
            onNext={() => handleStepComplete('specifications')}
            onBack={() => handleStepBack('type-selection')}
          />
        );
      case 'specifications':
        return (
          <SpecificationsForm
            onNext={() => handleStepComplete('variations')}
            onBack={() => handleStepBack('product-info')}
          />
        );
      case 'variations':
        return (
          <VariationsForm
            onComplete={() => handleStepComplete()}
            onBack={() => handleStepBack('specifications')}
          />
        );
      default:
        return (
          <TypeSelectionForm
            onNext={() => handleStepComplete('product-info')}
          />
        );
    }
  };

  return (
    <VerificationGuard feature="products">
      <ProductCreationLayout>
        {renderCurrentStep()}
      </ProductCreationLayout>
    </VerificationGuard>
  );
};

const ProductCreationPage = () => {
  return (
    <Suspense fallback={<FullScreenLoader text="Loading product creation..." />}>
      <ProductCreationPageContent />
    </Suspense>
  );
};

export default ProductCreationPage;
